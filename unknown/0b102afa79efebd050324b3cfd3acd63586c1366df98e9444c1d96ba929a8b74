from playwright.sync_api import Page

from src.Dweb.EC.dweb_ele.dweb_home.dweb_home_ele import *
from src.Dweb.EC.dweb_ele.dweb_search.dweb_search_result_ele import *
from src.common.commfunc import empty_cart
from src.common.commonui import close_advertise_on_home
from src.config.base_config import TEST_URL
from src.config.weee.log_help import log
from src.Dweb.EC.dweb_pages.dweb_common_page import DWebCommonPage


class HomeSearchPage(DWebCommonPage):
    """
    该类主要描述了如何在首页搜索商品，并对搜索到结果进行校验和加购
    """
    def __init__(self, page: Page, header):
        super().__init__(page, header)

    def search(self, key):
        """
        该方法包含以下功能：
        1. 根据传入的key, 在首页搜索框搜索，搜索到结果后，加购4个商品
        """
        self.page.goto(TEST_URL)
        self.page.wait_for_timeout(10000)
        close_advertise_on_home(self.page)
        # 清空购物车
        try:
            empty_cart(self.header)
        except Exception as e:
            log.info("清空购物车发生异常" + str(e))

        # 1. 根据key搜索
        self.FE.ele(ele_home_search_span).click()
        self.FE.ele(ele_home_search_input).fill(key)
        self.page.wait_for_timeout(1000)
        self.FE.ele(ele_home_search_button).click()

        # 2. 将搜索的结果加入购物车
        self.page.wait_for_timeout(1000)
        self.page.wait_for_load_state("networkidle")
        search_results = self.FE.eles(ele_search_result_add_to_cart)
        if search_results:
            for index, item in enumerate(search_results):
                try:
                    item.evaluate('(item) => item.click()')
                    # item.click()
                except Exception as e:
                    log.info("加购按钮点击失败")
                # self.page.reload()
                self.page.wait_for_timeout(1000)
                if index == 3:
                    break
        else:
            log.info(f"搜索没有结果, key={key}")

    def hot_key_search(self):
        """
        该方法包含以下功能：
        1. 在首页搜索框搜索热词，搜索到结果后，加购4个商品, 注意热词是系统根据大数据自动生成的，不需要用户输入
        """
        self.page.goto(TEST_URL)
        self.page.wait_for_timeout(10000)
        close_advertise_on_home(self.page)

        # 清空购物车
        try:
            empty_cart(self.header)
        except Exception as e:
            log.info("清空购物车发生异常" + str(e))

        self.FE.ele(ele_home_search_span).click()
        self.page.wait_for_timeout(3000)
        # all_search_hot_key就是系统推荐的热词，点击搜索的input框自动弹出
        all_search_hot_key = self.FE.eles(ele_hot_key_searches)

        # 带火焰图标的热词和普通热词一起
        for i in range(len(all_search_hot_key)):
            # 这里必须每次重新查找热词，否则由于页面刷新，原来的元素（ID变化）不存在，导致失败
            eles = self.FE.eles(ele_hot_key_searches)
            assert eles, f"当前页面没有hot key"
            hot_key_text = eles[i].text_content()
            log.info(hot_key_text)
            self.page.wait_for_load_state('networkidle', timeout=60000)
            eles[i].click()

            # 2. 将搜索的结果加入购物车
            self.page.wait_for_timeout(3000)
            self.page.wait_for_load_state('networkidle', timeout=60000)
            search_results = self.FE.eles(ele_search_result_add_to_cart)
            if search_results:
                for index, item in enumerate(search_results):
                    try:
                        # item.evaluate('(item) => item.click()')
                        item.click()
                    except Exception as e:
                        log.info("加购按钮点击失败")
                    # self.page.reload()
                    self.page.wait_for_timeout(1000)
                    if index == 3:
                        break
            else:
                log.info(f"搜索没有结果, key={hot_key_text}")

            self.page.goto(TEST_URL)
            self.page.wait_for_timeout(1500)
            self.FE.ele(ele_home_search_span).click()
            self.page.wait_for_timeout(3000)






