from typing import List
from playwright.sync_api import Page, Locator
from src.config.base_config import TEST_URL
from playwright._impl._element_handle import ElementHandle
from src.config.weee.log_help import log

ele_home_popup = u"//button[@type='button' and @aria-label='Close' and @class='ant-modal-close']"
ele_home_advertise = u"//div[@class='ant-modal-body']//img[@alt='Close']"
ele_home_advertise_h5 = u"//div[@data-component='CroppedImage']/img[contains(@aria-label, 'close button')]"


def close_popup_on_home(page: Page):
    try:
        page.wait_for_selector(selector=ele_home_popup, timeout=5000).click()
    except Exception as e:
        log.info("没有通知弹窗" + str(e))


def close_advertise_on_home(page: Page):
    try:
        page.wait_for_selector(selector=ele_home_advertise, timeout=5000).click()
    except Exception as e:
        log.info("没有广告弹窗" + str(e))

def close_advertise_on_home_h5(page: Page):
    try:
        page.wait_for_selector(selector=ele_home_advertise_h5, timeout=5000).click()
    except Exception as e:
        log.info("没有广告弹窗" + str(e))


def ele(page: Page, selector, timeout=5000):
    try:
        return page.wait_for_selector(selector, timeout=timeout)
    except Exception as e:
        log.debug(f"没有找到元素，selector={selector}" + str(e))


def eles(page: Page, selector, timeout=5000):
    try:
        return page.query_selector_all(selector)
    except Exception as e:
        log.debug("没有找到元素，selector={selector}")

def scroll_one_page_until(page:Page, element, timeout=2000):
    """
    翻页，直到某元素出现, 如果
    :param page: page
    :param element: 直到该元素出现
    :param timeout: 每翻一页等待的时间
    :return:
    """
    n = 0
    while True:
        page.evaluate('window.scrollBy(0, window.innerHeight)')
        page.wait_for_timeout(timeout)
        if page.query_selector(element):
            page.query_selector(element).scroll_into_view_if_needed()
            break

        if n > 20:
            return {"error": f"未找到{element}元素"}
        n += 1

def scroll_n_page(page: Page, n: int, timeout=2000):
    """
    连翻n页，主要用于h5的waterfall
    :param page: page
    :param n: 共翻多少页
    :param timeout: 每翻一页的等待时间
    :return:
    """
    for i in range(n):
        page.evaluate('window.scrollBy(0, window.innerHeight)')
        page.wait_for_timeout(timeout)




class FindElement:
    def __init__(self, page: Page):
        self.page = page
    def ele(self, selector, timeout=5000) -> ElementHandle | None:
        try:
            return self.page.wait_for_selector(selector, timeout=timeout)
        except Exception as e:
            log.debug(f"没有找到元素，selector={selector}" + str(e))
            return None

    def eles(self, selector, timeout=5000) -> List[ElementHandle] | None:
        try:
            return self.page.query_selector_all(selector)
        except Exception as e:
            log.debug(f"没有找到元素，selector={selector}" + str(e))
            return None

    def pele(self, selector) -> Locator | None:
        try:
            return self.page.locator(selector)
        except Exception as e:
            log.debug("没有返回正确的locator" + str(e))
            return None


def home_init(page: Page):
    page.goto(TEST_URL)
    page.wait_for_timeout(5000)

    close_popup_on_home(page)
    close_advertise_on_home(page)

def home_init_h5(page: Page):
    page.goto(TEST_URL)
    page.wait_for_timeout(5000)

    # close_popup_on_home(page)
    close_advertise_on_home_h5(page)
