from src.config.weee.log_help import log
from src.utils.HttpRequest import HttpRequest as HR

def get_track_data_tb1(user_id, platform, begin_at, end_at):
    res = HR.request({
        "method": "post",
        "path": "http://10.201.110.18:8888/api/datatrack/clickhousedata",
        "request_body": {
            "userId": user_id,
            "platform": platform,
            "beginAt": begin_at,
            "endAt": end_at
        }
    })
    return res.json()

def get_track_data_tb1_v2(begin_at=None, end_at=None):
    where = ""
    if begin_at and end_at:
        where = f"__time>='{begin_at}' and __time<='{end_at}'"
    elif begin_at and not end_at:
        where = f"__time>='{begin_at}'"
    elif end_at and not begin_at:
        where = f"__time<='{end_at}'"
    elif not begin_at and not end_at:
        where = '2>1'
    else:
        log.debug("条件输入不正确，请检查条件")

    query = f"""
                SELECT
                  "__time",
                  "l0_sdk_version",
                  "l0_event_id",
                  "l0_d2_validated",
                  "l0_view_id",
                  "l0_ip",
                  "l0_language",
                  "l0_event_time",
                  "l0_zipcode",
                  "l0_user_id",
                  "l0_referer_page_key",
                  "l0_os_version",
                  "l0_screen_width",
                  "l0_screen_height",
                  "l0_referer_view_id",
                  "l0_referer_page_ctx_app",
                  "l0_d2",
                  "l0_referer_page_url",
                  "l0_bu",
                  "message_id",
                  "params",
                  "l0_platform",
                  "l0_user_agent",
                  "l0_page_key",
                  "l0_page_ctx_app",
                  "l0_app_version",
                  "l0_os_language",
                  "l0_st",
                  "l0_store",
                  "l0_event_type",
                  "l0_device_id",
                  "l0_session_id",
                  "l0_page_url"
                FROM weee_data.data_tracking_local 
                where {where} 
                ORDER BY __time DESC limit 5000000
            """
    res = HR.request(
        data={
            "path": "http://tb1-data-355373674.us-east-2.elb.amazonaws.com:8123/",
            "method": "post",
            "headers": {
                "Content-Type": r"text/plain;charset=UTF-8",
                "Authorization": r"Basic ZGF0YV9wbGF0Zm9ybTo3dlNpRVcyZTJkMzJwNTZN"
            },
            "param": {
                "add_http_cors_header": 1,
                "default_format": "JSONCompact",
                "max_result_rows": 5000000,
                "max_result_bytes": 10000000000000000,
                "result_overflow_mode": "break"
            },

            "request_body": query
        },
        params_type="form"
    )

    keys = ['__time', 'l0_sdk_version', 'l0_event_id', 'l0_d2_validated', 'l0_view_id', 'l0_ip', 'l0_language',
            'l0_event_time', 'l0_zipcode', 'l0_user_id', 'l0_referer_page_key', 'l0_os_version',
            'l0_screen_width',
            'l0_screen_height', 'l0_referer_view_id', 'l0_referer_page_ctx_app', 'l0_d2', 'l0_referer_page_url',
            'l0_bu', 'message_id', 'params', 'l0_platform', 'l0_user_agent', 'l0_page_key', 'l0_page_ctx_app',
            'l0_app_version', 'l0_os_language', 'l0_st', 'l0_store', 'l0_event_type', 'l0_device_id',
            'l0_session_id', 'l0_page_url']
    render_data = []
    if type(res.json()['data']) == list and res.json()['data']:
        for item in res.json()['data']:
            new_dict = dict(zip(keys, item))
            render_data.append(new_dict)
        return {
            "code": 200,
            "data": render_data
        }
    else:
        return {
            "code": 500,
            "data": "未获取到线上埋点数据"
        }



def get_track_data_online(user_id, platform, begin_at, end_at):
    query = f"""
                SELECT
                  "__time",
                  "l0_sdk_version",
                  "l0_event_id",
                  "l0_d2_validated",
                  "l0_view_id",
                  "l0_ip",
                  "l0_language",
                  "l0_event_time",
                  "l0_zipcode",
                  "l0_user_id",
                  "l0_referer_page_key",
                  "l0_os_version",
                  "l0_screen_width",
                  "l0_screen_height",
                  "l0_referer_view_id",
                  "l0_referer_page_ctx_app",
                  "l0_d2",
                  "l0_referer_page_url",
                  "l0_bu",
                  "message_id",
                  "params",
                  "l0_platform",
                  "l0_user_agent",
                  "l0_page_key",
                  "l0_page_ctx_app",
                  "l0_app_version",
                  "l0_os_language",
                  "l0_st",
                  "l0_store",
                  "l0_event_type",
                  "l0_device_id",
                  "l0_session_id",
                  "l0_page_url"
                FROM weee_data.data_tracking_local 
                where l0_user_id='{user_id}' and l0_platform='{platform}' and __time>='{begin_at}' and __time<='{end_at}' 
                ORDER BY __time DESC limit 5000
            """
    res = HR.request(
        data={
            "path": "http://clickhouse-http-endpoint-1303890441.us-west-2.elb.amazonaws.com:8123/",
            "method": "post",
            "headers": {
                "Content-Type": r"text/plain;charset=UTF-8",
                "Authorization": r"Basic ZGF0YV9wbGF0Zm9ybTo3dlNpRVcyZTJkMzJwNTZN"
            },
            "param": {
                "add_http_cors_header": 1,
                "default_format": "JSONCompact",
                "max_result_rows": 5000,
                "max_result_bytes": 10000000000,
                "result_overflow_mode": "break"
            },

            "request_body": query
        },
        params_type="form"
    )

    keys = ['__time', 'l0_sdk_version', 'l0_event_id', 'l0_d2_validated', 'l0_view_id', 'l0_ip', 'l0_language',
            'l0_event_time', 'l0_zipcode', 'l0_user_id', 'l0_referer_page_key', 'l0_os_version',
            'l0_screen_width',
            'l0_screen_height', 'l0_referer_view_id', 'l0_referer_page_ctx_app', 'l0_d2', 'l0_referer_page_url',
            'l0_bu', 'message_id', 'params', 'l0_platform', 'l0_user_agent', 'l0_page_key', 'l0_page_ctx_app',
            'l0_app_version', 'l0_os_language', 'l0_st', 'l0_store', 'l0_event_type', 'l0_device_id',
            'l0_session_id', 'l0_page_url']
    render_data = []
    if type(res.json()['data']) == list and res.json()['data']:
        for item in res.json()['data']:
            new_dict = dict(zip(keys, item))
            render_data.append(new_dict)
        return {
            "code": 200,
            "data": render_data
        }
    else:
        return {
            "code": 500,
            "data": "未获取到线上埋点数据"
        }


def get_track_data_online_v2(begin_at, end_at):
    where = ""
    if begin_at and end_at:
        where = f"__time>='{begin_at}' and __time<='{end_at}'"
    elif begin_at and not end_at:
        where = f"__time>='{begin_at}'"
    elif end_at and not begin_at:
        where = f"__time<='{end_at}'"
    elif not begin_at and not end_at:
        where = '2>1'
    else:
        log.debug("条件输入不正确，请检查条件")
    query = f"""
                SELECT
                  "__time",
                  "l0_sdk_version",
                  "l0_event_id",
                  "l0_d2_validated",
                  "l0_view_id",
                  "l0_ip",
                  "l0_language",
                  "l0_event_time",
                  "l0_zipcode",
                  "l0_user_id",
                  "l0_referer_page_key",
                  "l0_os_version",
                  "l0_screen_width",
                  "l0_screen_height",
                  "l0_referer_view_id",
                  "l0_referer_page_ctx_app",
                  "l0_d2",
                  "l0_referer_page_url",
                  "l0_bu",
                  "message_id",
                  "params",
                  "l0_platform",
                  "l0_user_agent",
                  "l0_page_key",
                  "l0_page_ctx_app",
                  "l0_app_version",
                  "l0_os_language",
                  "l0_st",
                  "l0_store",
                  "l0_event_type",
                  "l0_device_id",
                  "l0_session_id",
                  "l0_page_url"
                FROM weee_data.data_tracking_local 
                where {where} 
                ORDER BY __time DESC limit 800
            """
    res = HR.request(
        data={
            "path": "http://clickhouse-http-endpoint-1303890441.us-west-2.elb.amazonaws.com:8123/",
            "method": "post",
            "headers": {
                "Content-Type": r"text/plain;charset=UTF-8",
                "Authorization": r"Basic ZGF0YV9wbGF0Zm9ybTo3dlNpRVcyZTJkMzJwNTZN"
            },
            "param": {
                "add_http_cors_header": 1,
                "default_format": "JSONCompact",
                "max_result_rows": 50000000,
                "max_result_bytes": 10000000000000000,
                "result_overflow_mode": "break"
            },

            "request_body": query
        },
        params_type="form"
    )

    keys = ['__time', 'l0_sdk_version', 'l0_event_id', 'l0_d2_validated', 'l0_view_id', 'l0_ip', 'l0_language',
            'l0_event_time', 'l0_zipcode', 'l0_user_id', 'l0_referer_page_key', 'l0_os_version',
            'l0_screen_width',
            'l0_screen_height', 'l0_referer_view_id', 'l0_referer_page_ctx_app', 'l0_d2', 'l0_referer_page_url',
            'l0_bu', 'message_id', 'params', 'l0_platform', 'l0_user_agent', 'l0_page_key', 'l0_page_ctx_app',
            'l0_app_version', 'l0_os_language', 'l0_st', 'l0_store', 'l0_event_type', 'l0_device_id',
            'l0_session_id', 'l0_page_url']
    render_data = []
    if type(res.json()['data']) == list and res.json()['data']:
        for item in res.json()['data']:
            new_dict = dict(zip(keys, item))
            render_data.append(new_dict)
        return {
            "code": 200,
            "data": render_data
        }
    else:
        return {
            "code": 500,
            "data": "未获取到线上埋点数据"
        }


if __name__ == '__main__':
    res = get_track_data_tb1_v2("2024-07-23 00:00:00", "2024-07-23 23:59:59")
    print("res===>", res)