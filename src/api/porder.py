import datetime

from src.common.get_header import login_header
from src.config.base_config import BASE_URL
from src.utils.HttpRequest import HttpRequest as HR


def update_zipcode_v1(headers, zipcode):
    """#update_zipcode_v1"""
    res = HR.request({
        "method": "put",
        "path": BASE_URL + "/ec/so/porder/zipcode",
        "headers": headers,
        "request_body": {
            "zipcode": str(zipcode)
        }
    })
    return res.json()


def query_simple_preorder_v1(headers):
    res = HR.request({
        "method": "get",
        "path": BASE_URL + "/ec/so/porder/simple",
        "headers": headers
    })
    return res.json()


def query_porder_v5(headers, cart_domain: str = 'grocery'):
    res = HR.request({
        "method": "get",
        "path": BASE_URL + "/ec/so/porder/v5",
        "headers": headers,
        "param": {
            "cart_domain": cart_domain
        }
    })
    return res.json()


def update_pickup_date(headers, _date):
    data = {"delivery_pickup_date": _date, "force": 0}
    res = HR.request({
        "method": "put",
        "path": BASE_URL + "/ec/so/porder/date",
        "headers": headers,
        "request_body": data
    })

    return res.json()


if __name__ == '__main__':
    headers = login_header(email='<EMAIL>', password='A1234567')
    # res = update_pickup_date(headers, (datetime.datetime.now() + datetime.timedelta(days=2)).strftime(
    #     '%Y-%m-%d'))
    res = update_zipcode_v1(headers, "98011")
    print(res)
