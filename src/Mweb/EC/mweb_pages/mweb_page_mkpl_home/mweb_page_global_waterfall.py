from playwright.sync_api import Page

from src.Mweb.EC.mweb_ele.mweb_mkpl_home.mweb_global_waterfall import (
    ele_global_intro_popup,
    ele_global_intro_close_button,
    ele_mkpl_banner_arrays_3,
    ele_mkpl_banner_arrays_1,
    ele_coupon_list_section,
    ele_coupon_list_icon
)
from src.Mweb.EC.mweb_pages.mweb_page_common.mweb_page_common_operations import PageH5CommonOperations
from src.config.weee.log_help import log


class MWebPageGlobalWaterfall(PageH5CommonOperations):
    """
    全球购瀑布流页面操作类
    """
    def __init__(self, page: Page, header, browser_context):
        super().__init__(page, header)
        self.bc = browser_context
        self.page.goto("https://www.sayweee.com/zh/mkpl/waterfall")
        self.page.wait_for_timeout(5000)
        # 关闭首页广告
        self.close_advertisement_in_homepage()
        
    def handle_global_intro_popup(self):
        """
        处理全球购介绍弹窗
        """
        # 使用 ele 方法检查元素是否存在，并返回元素对象
        popup_element = self.FE.ele(ele_global_intro_popup, timeout=3000)
        
        # 如果元素存在且可见，则关闭弹窗
        if popup_element and popup_element.is_visible():
            log.info("检测到全球购介绍弹窗，准备关闭")
            # 点击关闭按钮
            close_button = self.FE.ele(ele_global_intro_close_button, timeout=3000)
            if close_button:
                close_button.click()
                log.info("已关闭全球购介绍弹窗")
                # 等待弹窗关闭完成
                self.page.wait_for_timeout(2000)  # 多等待一会以确保弹窗完全关闭
            return True
        return False


    def click_banner_array_1(self):
        """
        点击 Marketplace Banner Array 1 元素
        """
        # 定义元素
        banner_element = self.FE.ele(ele_mkpl_banner_arrays_1, timeout=5000)
        assert banner_element, "未找到 Marketplace Banner Array 1 元素"

        # 如果元素不在可视区域内，需要滚动到元素位置
        if not banner_element.is_visible():
            banner_element.scroll_into_view_if_needed()
            self.page.wait_for_timeout(1000)

        # 点击元素
        banner_element.click()
        log.info("已点击 Marketplace Banner Array 1 元素")

        # 等待页面加载
        self.page.wait_for_timeout(5000)
        self.page.wait_for_load_state("networkidle", timeout=60000)

        # 验证跳转结果
        current_url = self.page.url
        expected_url_fragment = "mkpl/top-items"
        assert expected_url_fragment in current_url, f"页面链接 {current_url} 不包含 '{expected_url_fragment}'"

        # 检查URL是否包含所有必要的参数
        assert "tab=hot_selling" in current_url, f"URL不包含 'tab' 参数"
        assert "category=feature" in current_url, f"URL不包含 'category' 参数"

        log.info(f"成功点击 Marketplace Banner Array 1 元素，并跳转到正确的目标页面")
        return current_url

    
    def click_banner_array_3(self):
        """
        点击 Marketplace Banner Array 3 元素
        """
        # 定义元素
        banner_element = self.FE.ele(ele_mkpl_banner_arrays_3, timeout=5000)
        assert banner_element, "未找到 Marketplace Banner Array 3 元素"
        
        # 如果元素不在可视区域内，需要滚动到元素位置
        if not banner_element.is_visible():
            banner_element.scroll_into_view_if_needed()
            self.page.wait_for_timeout(1000)
        
        # 点击元素
        banner_element.click()
        log.info("已点击 Marketplace Banner Array 3 元素")
        
        # 等待页面加载
        self.page.wait_for_timeout(5000)
        self.page.wait_for_load_state("networkidle", timeout=60000)
        
        # 验证跳转结果
        current_url = self.page.url
        expected_url_fragment = "mkpl/global"
        assert expected_url_fragment in current_url, f"页面链接 {current_url} 不包含 '{expected_url_fragment}'"
        
        # 检查URL是否包含所有必要的参数
        assert "mode=sub_page" in current_url, f"URL不包含 'mode=sub_page' 参数"
        assert "hide_activity_pop=1" in current_url, f"URL不包含 'hide_activity_pop=1' 参数"
        
        log.info(f"成功点击 Marketplace Banner Array 3 元素，并跳转到正确的目标页面")
        return current_url
    
    def click_coupon_list_icon(self):
        """
        点击优惠券图标元素
        """
        # 在页面中找到 coupon_list 元素
        coupon_list = self.FE.ele(ele_coupon_list_section, timeout=5000)
        assert coupon_list, "未找到优惠券列表元素"
        log.info("已找到优惠券列表元素")

        # 如果元素不在可视区域内，需要滚动到元素位置
        if not coupon_list.is_visible():
            coupon_list.scroll_into_view_if_needed()
            self.page.wait_for_timeout(1000)
            log.info("已滚动到优惠券列表元素位置")

        # 找到优惠券图标元素并点击
        coupon_icon = self.FE.ele(ele_coupon_list_icon, timeout=5000)
        assert coupon_icon, "未找到优惠券图标元素"
        
        # 点击优惠券图标元素
        coupon_icon.click()
        log.info("已点击优惠券图标元素")

        # 等待页面加载
        self.page.wait_for_timeout(5000)
        self.page.wait_for_load_state("networkidle", timeout=60000)

        # 断言页面链接包含 "mkpl/coupon/landing"
        current_url = self.page.url
        expected_url_fragment = "mkpl/coupon/landing"
        assert expected_url_fragment in current_url, f"页面链接 {current_url} 不包含 '{expected_url_fragment}'"

        log.info(f"成功点击优惠券图标元素，并跳转到 {current_url}")
        return current_url
