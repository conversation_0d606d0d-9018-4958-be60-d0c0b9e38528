"""
<AUTHOR>  li.zhu
@Version        :  V1.0.0
------------------------------------
@File           :   mweb_page_category.py
@Description    :  移动端分类页面类
@CreateTime     :  2025/4/25 10:30
@Software       :  PyCharm
------------------------------------
@ModifyTime     :  2025/4/25 10:30
"""
import time

import allure
from playwright.sync_api import Locator

from src.Mweb.EC.mweb_ele.mweb_cart import mweb_cart_ele
from src.Mweb.EC.mweb_ele.mweb_category import mweb_category_ele
from src.Mweb.EC.mweb_ele.mweb_home import mweb_home_ele
from src.config.base_config import TEST_URL
from src.config.weee.log_help import log
from playwright.sync_api import Page
from src.Mweb.EC.mweb_pages.mweb_common_page import MWebCommonPage
from src.common.commonui import scroll_one_page_until


class MWebCategorypage(MWebCommonPage):
    def __init__(self, page: Page, header, browser_context, zipcode: str = "98011", page_url=None):
        super().__init__(page, header)
        self.bc = browser_context
        # 进入首页
        self.bc = browser_context
        # 直接进入购物车
        self.page.goto(TEST_URL + page_url + "?joinEnki=true")
        self.page.wait_for_timeout(2000)

    def category_filter_delivery_type(self, filter_delivery_type: Locator):
        """
        该方法包含以下功能：
        1. 分类页点击勾选filter_delivery_type对应的filter来筛选不同的搜索结果
        """
        filter_delivery_type.click()
        self.page.wait_for_timeout(2000)

    def category_filter_delivery_type_check(self, filter_delivery_type: Locator):
        """
        该方法包含以下功能：
        1. 分类页点击勾选filter_delivery_type对应的filter来筛选不同的搜索结果
        """
        filter_delivery_type.click()
        self.page.wait_for_timeout(2000)

    def category_filter_delivery_type_uncheck(self, filter_delivery_type):
        """
        该方法包含以下功能：
        1. 分类页点击[取消勾选]filter_delivery_type对应的filter，来筛选不同的搜索结果
        """
        filter_delivery_type.uncheck()
        self.page.wait_for_timeout(2000)

    def category_filter_product_type_check(self, filter_product_type):
        """
        该方法包含以下功能：
        1. 分类页点击勾选filter:Product type, 根据产品类型来筛选产品，获得不同的搜索结果
        """
        filter_product_type.check()
        self.page.wait_for_timeout(2000)

    def category_filter_product_type_uncheck(self, filter_product_type):
        """
        该方法包含以下功能：
        1. 分类页点击取消勾选filter:Product type, 根据产品类型来筛选产品，获得不同的搜索结果
        """
        filter_product_type.uncheck()
        self.page.wait_for_timeout(2000)

    def special_category_filter_sub_category(self, sub_category):
        """
        该方法包含以下功能：
        1. 根据传入的特殊分类页点击切换子分类
        """
        self.FE.ele(sub_category).click()
        self.page.wait_for_timeout(2000)

    def add_product_to_cart(self, product_id):
        """
        该方法包含以下功能：
        1. 根据传入的product_id, 加购指定商品
        """
        product_id.click()
        self.page.wait_for_timeout(2000)

    def go_to_cart_from_category(self):
        """
        该方法包含以下功能：
        1. 点击购物车按钮，进入购物车页面
        """
        self.page.get_by_test_id("wid-mini-cart").click()
        self.page.wait_for_load_state("networkidle", timeout=60000)


    def _get_cart_count(self):
        """获取当前购物车商品数量"""
        try:
            cart_count_elem = self.page.locator("//span[contains(@class, 'CartIcon_cartIconCountQty')]")
            if cart_count_elem.is_visible(timeout=2000):
                count_text = cart_count_elem.text_content()
                if count_text and count_text.isdigit():
                    return int(count_text)
        except Exception as e:
            log.warning(f"获取购物车数量失败: {str(e)}")
        return 0



    def add_to_global_product_cart_from_category(self):
        """
        该方法包含以下功能：
        1. 分类页加购global类型的商品
        """
        # 进入deals分类页
        self.go_to_special_category_from_hone(mweb_home_ele.ele_deals)
        # 滚动到指定位置
        scroll_one_page_until(self.page, mweb_category_ele.ele_filter_reset)
        # 点击Delivery type = global
        delivery_type_global = self.page.get_by_test_id(mweb_category_ele.global_delivery_test_id)
        # 滚动到指定位置
        scroll_one_page_until(self.page, mweb_category_ele.ele_filter_reset)
        self.category_filter_delivery_type(delivery_type_global)
        # 加购global售卖第一个商品
        product_ids = self.page.query_selector_all("//div[@data-testid='btn-atc-plus']")
        assert product_ids, f"本地售卖没有商品"
        for index, item in enumerate(product_ids):
            try:
                self.add_product_to_cart(item)
            except Exception as e:
                log.info("按钮加购失败" + str(e))
            if index == 1:
                break
        # 滚动到指定位置
        scroll_one_page_until(self.page, mweb_category_ele.ele_filter_reset)
        # 再调一次，点击取消Delivery type = global
        self.category_filter_delivery_type(delivery_type_global)

    def add_to_alcohol_product_cart_from_category(self):
        """
        该方法包含以下功能：
        1. 分类页加购alcohol类型的商品
        """
        # 进入deals分类页
        self.go_to_special_category_from_hone(mweb_home_ele.ele_deals)
        # 滚动到指定位置
        scroll_one_page_until(self.page, mweb_category_ele.ele_filter_reset)
        # 点击Delivery type = Local Delivery
        delivery_type_local = self.page.get_by_test_id(mweb_category_ele.local_delivery_test_id)
        self.category_filter_delivery_type(delivery_type_local)

        self.page.wait_for_timeout(2000)
        product_ids = self.page.query_selector_all("//div[@data-testid='btn-atc-plus']")
        assert product_ids, f"本地售卖没有商品"
        for index, item in enumerate(product_ids):
            try:
                self.add_product_to_cart(item)
            except Exception as e:
                log.info("按钮加购失败" + str(e))
            if index == 1:
                break

        # 滚动到指定位置
        scroll_one_page_until(self.page, mweb_category_ele.ele_filter_reset)
        # 再调一次，点击取消Delivery type = Local Delivery
        self.category_filter_delivery_type(delivery_type_local)
        # 点击Delivery type = pantry
        delivery_type_pantry = self.page.get_by_test_id(mweb_category_ele.pantry_delivery_test_id)
        # 滚动到指定位置
        scroll_one_page_until(self.page, mweb_category_ele.ele_filter_reset)
        # 点击pantry
        self.category_filter_delivery_type(delivery_type_pantry)
        # 加购pantry售卖第一个商品
        product_ids = self.page.query_selector_all("//div[@data-testid='btn-atc-plus']")
        assert product_ids, f"本地售卖没有商品"
        for index, item in enumerate(product_ids):
            try:
                self.add_product_to_cart(item)
            except Exception as e:
                log.info("按钮加购失败" + str(e))
            if index == 1:
                break
        # 滚动到指定位置
        scroll_one_page_until(self.page, mweb_category_ele.ele_filter_reset)
        # 再调一次，点击取消Delivery type = pantry
        self.category_filter_delivery_type(delivery_type_pantry)

        # 点击Delivery type = global
        delivery_type_global = self.page.get_by_test_id(mweb_category_ele.global_delivery_test_id)
        # 滚动到指定位置
        scroll_one_page_until(self.page, mweb_category_ele.ele_filter_reset)
        self.category_filter_delivery_type(delivery_type_global)
        # 加购global售卖第一个商品
        product_ids = self.page.query_selector_all("//div[@data-testid='btn-atc-plus']")
        assert product_ids, f"本地售卖没有商品"
        for index, item in enumerate(product_ids):
            try:
                self.add_product_to_cart(item)
            except Exception as e:
                log.info("按钮加购失败" + str(e))
            if index == 1:
                break
        # 滚动到指定位置
        scroll_one_page_until(self.page, mweb_category_ele.ele_filter_reset)
        # 再调一次，点击取消Delivery type = global
        self.category_filter_delivery_type(delivery_type_global)
        # 切换到酒分类
        # self.special_category_filter_sub_category(mweb_category_ele.Alcohol)
        # 加购商品
        product_ids = self.page.query_selector_all("//div[@data-testid='btn-atc-plus']")
        assert product_ids, f"本地售卖没有商品"
        for index, item in enumerate(product_ids):
            try:
                self.add_product_to_cart(item)
            except Exception as e:
                log.info("按钮加购失败" + str(e))
            if index == 1:
                break

        # 8. 去购物车结算
        self.go_to_cart_from_category()


    def add_to_many_cart_from_category(self):
        """
        该方法包含以下功能：
        1. 分类页加购Deals类型的商品
        """
        # 进入deals分类页
        self.go_to_special_category_from_hone(mweb_home_ele.ele_deals)
        # 滚动到指定位置
        scroll_one_page_until(self.page, mweb_category_ele.ele_filter_reset)
        # 点击Delivery type = Local Delivery
        delivery_type_local = (self.page.locator(""))
                               # get_by_test_id(mweb_category_ele.local_delivery_test_id)
        self.category_filter_delivery_type(delivery_type_local)

        self.page.wait_for_timeout(2000)
        product_ids = self.page.query_selector_all("//div[@data-testid='btn-atc-plus']")
        assert product_ids, f"本地售卖没有商品"
        for index, item in enumerate(product_ids):
            try:
                self.add_product_to_cart(item)
            except Exception as e:
                log.info("按钮加购失败" + str(e))
            if index == 1:
                break

        # 滚动到指定位置
        scroll_one_page_until(self.page, mweb_category_ele.ele_filter_reset)
        # 再调一次，点击取消Delivery type = Local Delivery
        self.category_filter_delivery_type(delivery_type_local)
        # 点击Delivery type = pantry
        delivery_type_pantry = self.page.get_by_test_id(mweb_category_ele.pantry_delivery_test_id)
        # 滚动到指定位置
        scroll_one_page_until(self.page, mweb_category_ele.ele_filter_reset)
        # 点击pantry
        self.category_filter_delivery_type(delivery_type_pantry)
        # 加购pantry售卖第一个商品
        product_ids = self.page.query_selector_all("//div[@data-testid='btn-atc-plus']")
        assert product_ids, f"本地售卖没有商品"
        for index, item in enumerate(product_ids):
            try:
                self.add_product_to_cart(item)
            except Exception as e:
                log.info("按钮加购失败" + str(e))
            if index == 1:
                break
        # 滚动到指定位置
        scroll_one_page_until(self.page, mweb_category_ele.ele_filter_reset)
        # 再调一次，点击取消Delivery type = pantry
        self.category_filter_delivery_type(delivery_type_pantry)

        # 点击Delivery type = global
        delivery_type_global = self.page.get_by_test_id(mweb_category_ele.ele_global_delivery_xpath)
        # 滚动到指定位置
        scroll_one_page_until(self.page, mweb_category_ele.ele_filter_reset)
        self.category_filter_delivery_type(delivery_type_global)
        # 加购global售卖第一个商品
        product_ids = self.page.query_selector_all("//div[@data-testid='btn-atc-plus']")
        assert product_ids, f"本地售卖没有商品"
        for index, item in enumerate(product_ids):
            try:
                self.add_product_to_cart(item)
            except Exception as e:
                log.info("按钮加购失败" + str(e))
            if index == 1:
                break
        # 滚动到指定位置
        scroll_one_page_until(self.page, mweb_category_ele.ele_filter_reset)
        # 再调一次，点击取消Delivery type = global
        self.category_filter_delivery_type(delivery_type_global)
        # 切换到酒分类
        # self.special_category_filter_sub_category(mweb_category_ele.Alcohol)
        # 加购商品
        product_ids = self.page.query_selector_all("//div[@data-testid='btn-atc-plus']")
        assert product_ids, f"本地售卖没有商品"
        for index, item in enumerate(product_ids):
            try:
                self.add_product_to_cart(item)
            except Exception as e:
                log.info("按钮加购失败" + str(e))
            if index == 1:
                break

        # 8. 去购物车结算
        self.go_to_cart_from_category()

    @staticmethod
    def add_products_by_category_filter(page: Page, category_page, test_id, items_to_add=1):
        """
        在分类页面根据筛选条件添加商品

        Args:
            page: Playwright页面对象
            category_page: 分类页面对象
            test_id: 筛选按钮的test-id或XPath
            items_to_add: 要加购的商品数量

        Returns:
            int: 成功加购的商品数量
        """
        try:
            # 等待页面完全加载
            page.wait_for_load_state("networkidle", timeout=30000)
            page.wait_for_timeout(2000)

            log.info("开始筛选商品")

            # 点击筛选按钮
            page.get_by_test_id(mweb_category_ele.ele_filter_button).click()
            page.wait_for_timeout(2000)

            # 选择对应筛选条件 - 检查是否是XPath
            if isinstance(test_id, str) and test_id.startswith("//"):
                # 使用XPath选择器
                filter_btn = page.locator(test_id)
                filter_name = "筛选条件"
            else:
                # 使用test_id选择器
                filter_btn = page.get_by_test_id(test_id)
                filter_name = test_id

            if not filter_btn.is_visible(timeout=5000):
                log.error(f"未找到{filter_name}筛选按钮")
                # 尝试使用备用方法定位Global+
                if test_id == mweb_category_ele.ele_global_delivery:
                    log.info("尝试使用XPath定位Global+筛选按钮")
                    xpath_btn = page.locator(mweb_category_ele.ele_global_delivery_xpath)
                    if xpath_btn.is_visible(timeout=2000):
                        filter_btn = xpath_btn
                        log.info("成功使用XPath定位Global+筛选按钮")
                    else:
                        # 尝试使用文本内容定位
                        text_btn = page.locator("//div[contains(text(), 'Global+') or contains(text(), 'Global +')]")
                        if text_btn.is_visible(timeout=2000):
                            filter_btn = text_btn
                            log.info("成功使用文本内容定位Global+筛选按钮")
                        else:
                            return 0
                else:
                    return 0

            filter_btn.click()
            page.wait_for_timeout(1000)

            # 应用筛选
            page.get_by_test_id(mweb_category_ele.ele_filter_apply).click()
            page.wait_for_load_state("networkidle", timeout=30000)

            # 等待页面完全加载并稳定
            page.wait_for_timeout(3000)

            # 查找可加购的商品 - 尝试多种选择器
            log.info("开始查找可加购的商品")

            # 尝试滚动页面以确保商品加载
            try:
                page.evaluate("window.scrollBy(0, 300)")
                page.wait_for_timeout(1000)
                page.evaluate("window.scrollBy(0, 300)")
                page.wait_for_timeout(1000)
            except Exception as e:
                log.warning(f"滚动页面失败: {str(e)}")

            # 尝试使用多种选择器定位加购按钮
            selectors = [
                mweb_category_ele.ele_add_to_cart_xpath,
                "//div[@data-testid='btn-atc-plus']",
                "//button[contains(@class, 'add-to-cart')]",
                "//div[contains(@class, 'add-to-cart')]",
                "//div[contains(@class, 'add-button')]"
            ]

            product_buttons = []
            for selector in selectors:
                try:
                    buttons = page.query_selector_all(selector)
                    if buttons and len(buttons) > 0:
                        product_buttons = buttons
                        log.info(f"使用选择器 '{selector}' 找到{len(buttons)}个加购按钮")
                        break
                except Exception as e:
                    log.warning(f"使用选择器 '{selector}' 定位加购按钮失败: {str(e)}")

            if not product_buttons:
                # 尝试使用test_id选择器
                try:
                    buttons = page.get_by_test_id(mweb_category_ele.ele_add_to_cart_button).all()
                    if buttons and len(buttons) > 0:
                        product_buttons = buttons
                        log.info(f"使用test_id选择器找到{len(buttons)}个加购按钮")
                except Exception as e:
                    log.warning(f"使用test_id选择器定位加购按钮失败: {str(e)}")

            if not product_buttons:
                log.error("筛选后未找到可加购的商品")
                # 捕获当前页面截图以便调试
                try:
                    import time
                    screenshot_path = f"no_products_found_{int(time.time())}.png"
                    page.screenshot(path=screenshot_path)
                    log.info(f"页面截图已保存到: {screenshot_path}")
                except Exception as ss_e:
                    log.error(f"截图失败: {str(ss_e)}")
                return 0

            log.info(f"找到{len(product_buttons)}个可加购的商品")

            # 加购指定数量的商品
            added_count = 0
            for index, item in enumerate(product_buttons):
                if index >= items_to_add:
                    break
                try:
                    log.info(f"尝试加购第{index+1}个商品")
                    # 确保元素可见
                    item.scroll_into_view_if_needed()
                    page.wait_for_timeout(2000)  # 增加等待时间

                    # 尝试点击前检查元素是否可见和可点击
                    if item.is_visible() and item.is_enabled():
                        item.click(force=True)  # 使用force=True强制点击
                        page.wait_for_timeout(3000)  # 增加等待时间
                        added_count += 1
                        log.info(f"成功加购第{index+1}个商品")
                    else:
                        log.warning(f"第{index+1}个商品不可见或不可点击，尝试其他方法")
                        raise Exception("元素不可见或不可点击")
                except Exception as e:
                    log.error(f"加购第{index+1}个商品失败: {str(e)}")
            log.info(f"成功加购{added_count}个商品")
            return added_count

        except Exception as e:
            log.error(f"添加商品失败: {str(e)}")
            import traceback
            log.error(traceback.format_exc())
            return 0

    def add_products_from_home_by_filter(self, filter_name, filter_id, count=3):
        """从首页进入Deals分类，通过筛选添加商品

        Args:
            filter_name: 筛选名称
            filter_id: 筛选按钮ID
            count: 添加商品数量

        Returns:
            int: 成功添加的商品数量
        """
        try:
            log.info(f"开始添加{filter_name}商品")
            # 进入Deals分类
            self.go_to_special_category_from_hone(mweb_home_ele.ele_deals)
            self.page.wait_for_load_state("networkidle", timeout=30000)

            # 点击筛选按钮
            self.page.get_by_test_id(mweb_category_ele.ele_filter_button).click()
            self.page.wait_for_timeout(2000)

            # 选择对应筛选条件
            filter_btn = self.page.get_by_test_id(filter_id)
            if not filter_btn.is_visible(timeout=5000):
                log.error(f"未找到{filter_name}筛选按钮")
                return 0

            filter_btn.click()
            self.page.wait_for_timeout(1000)

            # 应用筛选
            self.page.get_by_test_id("btn-apply-filter").click()
            self.page.wait_for_load_state("networkidle", timeout=30000)

            # 添加商品到购物车
            added = 0
            product_ids = self.page.query_selector_all("//div[@data-testid='btn-atc-plus']")
            assert product_ids, f"{filter_name}筛选下没有商品"

            for index, item in enumerate(product_ids):
                try:
                    self.add_product_to_cart(item)
                    added += 1
                except Exception as e:
                    log.info(f"按钮加购失败: {str(e)}")
                if index == count - 1:
                    break

            return added
        except Exception as e:
            log.error(f"添加{filter_name}商品失败: {str(e)}")
            import traceback
            log.error(traceback.format_exc())
            return 0
def apply_filter_and_add_products(page: Page, filter_type, test_id, items_to_add=1, reset_after=False):
    """
    应用筛选条件并加购商品

    Args:
        page: Playwright页面对象
        filter_type: 筛选类型名称
        test_id: 筛选按钮的test-id
        items_to_add: 要加购的商品数量
        reset_after: 加购后是否重置筛选条件

    Returns:
        int: 成功加购的商品数量
    """
    with allure.step(f"应用{filter_type}筛选条件并加购商品"):
        try:
            # 点击筛选按钮
            page.get_by_test_id(mweb_category_ele.ele_filter_button).click()
            page.wait_for_timeout(2000)

            # 选择对应筛选条件 - 检查是否是XPath
            if test_id.startswith("//"):
                # 使用XPath选择器
                filter_btn = page.locator(test_id)
            else:
                # 使用test_id选择器
                filter_btn = page.get_by_test_id(test_id)

            if not filter_btn.is_visible(timeout=2000):
                log.error(f"未找到{filter_type}筛选按钮")
                return 0

            filter_btn.click()
            page.wait_for_timeout(1000)

            # 应用筛选
            page.get_by_test_id(mweb_category_ele.ele_filter_apply).click()
            page.wait_for_load_state("networkidle", timeout=30000)
            page.wait_for_timeout(2000)

            log.info(f"成功应用{filter_type}筛选条件")

            # 查找可加购的商品
            product_buttons = page.query_selector_all(mweb_category_ele.ele_add_to_cart_xpath)

            if not product_buttons:
                log.error(f"未找到可加购的{filter_type}商品")
                return 0

            log.info(f"找到{len(product_buttons)}个可加购的{filter_type}商品")

            # 加购指定数量的商品
            added_count = 0
            for index, item in enumerate(product_buttons):
                if index >= items_to_add:
                    break
                try:
                    log.info(f"尝试加购第{index+1}个{filter_type}商品")
                    # 确保元素可见
                    item.scroll_into_view_if_needed()
                    page.wait_for_timeout(1000)
                    item.click()
                    page.wait_for_timeout(2000)
                    added_count += 1
                    log.info(f"成功加购第{index+1}个{filter_type}商品")
                except Exception as e:
                    log.error(f"加购第{index+1}个{filter_type}商品失败: {str(e)}")
                    # 尝试使用JavaScript点击
                    try:
                        item.evaluate('(node) => node.click()')
                        page.wait_for_timeout(2000)
                        added_count += 1
                        log.info(f"使用JavaScript成功加购第{index+1}个{filter_type}商品")
                    except Exception as js_e:
                        log.error(f"使用JavaScript加购第{index+1}个{filter_type}商品也失败: {str(js_e)}")
                        continue

            log.info(f"成功加购{added_count}个{filter_type}商品")

            # 如果需要，重置筛选条件
            if reset_after:
                reset_filter(page)

            return added_count

        except Exception as e:
            log.error(f"应用{filter_type}筛选条件并加购商品失败: {str(e)}")
            import traceback
            log.error(traceback.format_exc())
            return 0


def reset_filter(page: Page):
    """
    重置筛选条件

    Args:
        page: Playwright页面对象

    Returns:
        bool: 重置是否成功
    """
    with allure.step("重置筛选条件"):
        try:
            # 点击筛选按钮
            page.get_by_test_id(mweb_category_ele.ele_filter_button).click()
            page.wait_for_timeout(2000)

            # 点击重置按钮
            page.get_by_test_id(mweb_category_ele.ele_filter_reset).click()
            page.wait_for_timeout(1000)

            # 应用筛选
            page.get_by_test_id(mweb_category_ele.ele_filter_apply).click()
            page.wait_for_load_state("networkidle", timeout=30000)
            page.wait_for_timeout(2000)

            log.info("成功重置筛选条件")
            return True

        except Exception as e:
            log.error(f"重置筛选条件失败: {str(e)}")
            import traceback
            log.error(traceback.format_exc())
            return False


def verify_cart_items(page: Page):
    """
    验证购物车中的商品

    Args:
        page: Playwright页面对象

    Returns:
        bool: 验证是否成功
    """
    with allure.step("验证购物车中的商品"):
        try:
            # 等待购物车商品加载
            page.wait_for_selector(mweb_cart_ele.ele_cart_normal_card, timeout=5000)

            # 获取购物车商品
            cart_items = page.query_selector_all(mweb_cart_ele.ele_cart_normal_card)
            if not cart_items:
                log.error("购物车中没有商品")
                return False

            log.info(f"购物车中有{len(cart_items)}个商品")
            return True

        except Exception as e:
            log.error(f"验证购物车商品失败: {str(e)}")
            return False


def apply_multiple_filters_and_add_products(page: Page, filters_config):
    """
    应用多个筛选条件并加购商品

    Args:
        page: Playwright页面对象
        filters_config: 筛选配置列表，每个配置包含 filter_type, test_id, items_to_add
                       例如: [{"filter_type": "Local Delivery", "test_id": "btn-filter-delivery_type-delivery_type_local", "items_to_add": 2}]
                       如果需要使用XPath选择器，可以直接在test_id中传入XPath字符串，例如: "//div[contains(@class, 'box-border')]"

    Returns:
        dict: 每种筛选类型成功加购的商品数量
    """
    with allure.step("应用多个筛选条件并加购商品"):
        try:
            results = {}
            total_added = 0

            for i, config in enumerate(filters_config):
                filter_type = config["filter_type"]
                test_id = config["test_id"]
                items_to_add = config.get("items_to_add", 1)

                # 最后一个筛选条件不需要重置
                reset_after = i < len(filters_config) - 1

                log.info(f"开始应用第{i+1}个筛选条件: {filter_type}")
                added = apply_filter_and_add_products(
                    page, filter_type, test_id, items_to_add, reset_after
                )

                results[filter_type] = added
                total_added += added

                log.info(f"完成第{i+1}个筛选条件: {filter_type}，加购{added}个商品")

            log.info(f"所有筛选条件应用完成，总共加购{total_added}个商品")
            return results

        except Exception as e:
            log.error(f"应用多个筛选条件并加购商品失败: {str(e)}")
            import traceback
            log.error(traceback.format_exc())
            return {}
