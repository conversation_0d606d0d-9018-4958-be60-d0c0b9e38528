{"name": "H5-社区搜索Accounts栏验证", "status": "broken", "statusDetails": {"message": "playwright._impl._errors.TimeoutError: Timeout 30000ms exceeded.", "trace": "self = <src.Mweb.EC.testcases.mweb_social.test_112741_h5_social_search_accounts_verify.TestH5HomePageCategoryVerify object at 0x00000245CAE47BD0>\nphone_page = {'context': <BrowserContext browser=<Browser type=<BrowserType name=chromium executable_path=C:\\Users\\<USER>\\App...in\\chrome.exe> version=120.0.6099.28>>, 'page': <Page url='https://www.sayweee.com/en/social/search?keyword=autotest'>}\nh5_autotest_header = {'Content-Type': 'application/json;charset=UTF-8', 'Zipcode': '98011', 'app-version': 'null', 'authorization': 'Bearer...v9_PSmGXx9kdIuaTcqJ6TffaahxcUzEwkALnHEPyrpZ31CEgxQrwVABoOdL6EMwIqUwZ9wpfh5f6WllneBaOZOcxJzf-KRILTbluyw6bxSVxRxNA', ...}\nh5_open_and_close_trace = None\n\n    @allure.title(\"H5-社区搜索Accounts栏验证\")\n    @pytest.mark.social\n    def test_112741_h5_social_search_accounts_verify(self, phone_page: dict, h5_autotest_header,\n                                                     h5_open_and_close_trace):\n        \"\"\"\n        H5-社区搜索Accounts栏验证\n        \"\"\"\n        _page: Page = phone_page[\"page\"]\n        _context = phone_page[\"context\"]\n        social = MWebPageSocial(_page, h5_autotest_header, _context)\n        # 首页category存在\n>       social.click_search(\"autotest\")\n\ntest_112741_h5_social_search_accounts_verify.py:25: \n_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _\n..\\..\\mweb_pages\\mweb_social_page\\mweb_page_social.py:30: in click_search\n    self._follow()\n..\\..\\mweb_pages\\mweb_social_page\\mweb_page_social.py:46: in _follow\n    self.page.locator(\"//span[text()='Follow']\").all()[0].click()\nD:\\qa-ui-dmweb\\vevn-dmweb\\Lib\\site-packages\\playwright\\sync_api\\_generated.py:15784: in click\n    self._sync(\nD:\\qa-ui-dmweb\\vevn-dmweb\\Lib\\site-packages\\playwright\\_impl\\_locator.py:159: in click\n    return await self._frame.click(self._selector, strict=True, **params)\nD:\\qa-ui-dmweb\\vevn-dmweb\\Lib\\site-packages\\playwright\\_impl\\_frame.py:484: in click\n    await self._channel.send(\"click\", locals_to_params(locals()))\nD:\\qa-ui-dmweb\\vevn-dmweb\\Lib\\site-packages\\playwright\\_impl\\_connection.py:62: in send\n    return await self._connection.wrap_api_call(\nD:\\qa-ui-dmweb\\vevn-dmweb\\Lib\\site-packages\\playwright\\_impl\\_connection.py:492: in wrap_api_call\n    return await cb()\n_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _\n\nself = <playwright._impl._connection.Channel object at 0x00000245CD391350>\nmethod = 'click'\nparams = {'selector': \"//span[text()='Follow'] >> nth=0\", 'strict': True}\nreturn_as_dict = False\n\n    async def inner_send(\n        self, method: str, params: Optional[Dict], return_as_dict: bool\n    ) -> Any:\n        if params is None:\n            params = {}\n        callback = self._connection._send_message_to_server(\n            self._object, method, params\n        )\n        if self._connection._error:\n            error = self._connection._error\n            self._connection._error = None\n            raise error\n        done, _ = await asyncio.wait(\n            {\n                self._connection._transport.on_error_future,\n                callback.future,\n            },\n            return_when=asyncio.FIRST_COMPLETED,\n        )\n        if not callback.future.done():\n            callback.future.cancel()\n>       result = next(iter(done)).result()\nE       playwright._impl._errors.TimeoutError: Timeout 30000ms exceeded.\n\nD:\\qa-ui-dmweb\\vevn-dmweb\\Lib\\site-packages\\playwright\\_impl\\_connection.py:100: TimeoutError"}, "description": "\n        H5-社区搜索Accounts栏验证\n        ", "start": *************, "stop": *************, "uuid": "d54063df-48d6-4c9c-a24d-6ea231b3a9df", "historyId": "0dd6c423b804140fbf2710451f272bc6", "testCaseId": "0dd6c423b804140fbf2710451f272bc6", "fullName": "src.Mweb.EC.testcases.mweb_social.test_112741_h5_social_search_accounts_verify.TestH5HomePageCategoryVerify#test_112741_h5_social_search_accounts_verify", "labels": [{"name": "story", "value": "H5-社区搜索Accounts栏验证"}, {"name": "tag", "value": "social"}, {"name": "tag", "value": "case"}, {"name": "tag", "value": "test"}, {"name": "tag", "value": "mweb_regression"}, {"name": "parentSuite", "value": "src.Mweb.EC.testcases.mweb_social"}, {"name": "suite", "value": "test_112741_h5_social_search_accounts_verify"}, {"name": "subSuite", "value": "TestH5HomePageCategoryVerify"}, {"name": "host", "value": "SHLAP10427"}, {"name": "thread", "value": "42312-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "src.Mweb.EC.testcases.mweb_social.test_112741_h5_social_search_accounts_verify"}]}