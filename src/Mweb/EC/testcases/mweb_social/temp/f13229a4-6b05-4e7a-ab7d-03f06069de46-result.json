{"name": "H5-社区搜索Accounts栏验证", "status": "passed", "start": *************, "stop": *************, "uuid": "433e0227-bd04-4ae7-b8e8-09827d45f2cd", "historyId": "0dd6c423b804140fbf2710451f272bc6", "testCaseId": "0dd6c423b804140fbf2710451f272bc6", "fullName": "src.Mweb.EC.testcases.mweb_social.test_112741_h5_social_search_accounts_verify.TestH5HomePageCategoryVerify#test_112741_h5_social_search_accounts_verify", "labels": [{"name": "story", "value": "H5-社区搜索Accounts栏验证"}, {"name": "tag", "value": "social"}, {"name": "tag", "value": "case"}, {"name": "tag", "value": "test"}, {"name": "tag", "value": "mweb_regression"}, {"name": "parentSuite", "value": "src.Mweb.EC.testcases.mweb_social"}, {"name": "suite", "value": "test_112741_h5_social_search_accounts_verify"}, {"name": "subSuite", "value": "TestH5HomePageCategoryVerify"}, {"name": "host", "value": "SHLAP10427"}, {"name": "thread", "value": "23652-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "src.Mweb.EC.testcases.mweb_social.test_112741_h5_social_search_accounts_verify"}]}