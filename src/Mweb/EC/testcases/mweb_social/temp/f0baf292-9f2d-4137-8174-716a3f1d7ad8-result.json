{"name": "H5-社区搜索Accounts栏验证", "status": "passed", "description": "\n        H5-社区搜索Accounts栏验证\n        ", "parameters": [{"name": "__pytest_repeat_step_number", "value": "3"}], "start": *************, "stop": *************, "uuid": "19ab9d8d-efac-4e8d-b253-22faf1b6b543", "historyId": "baaff8b67d37e0b25d0c7564230daf36", "testCaseId": "0dd6c423b804140fbf2710451f272bc6", "fullName": "src.Mweb.EC.testcases.mweb_social.test_112741_h5_social_search_accounts_verify.TestH5HomePageCategoryVerify#test_112741_h5_social_search_accounts_verify", "labels": [{"name": "story", "value": "H5-社区搜索Accounts栏验证"}, {"name": "tag", "value": "repeat(5)"}, {"name": "tag", "value": "social"}, {"name": "tag", "value": "case"}, {"name": "tag", "value": "test"}, {"name": "tag", "value": "mweb_regression"}, {"name": "parentSuite", "value": "src.Mweb.EC.testcases.mweb_social"}, {"name": "suite", "value": "test_112741_h5_social_search_accounts_verify"}, {"name": "subSuite", "value": "TestH5HomePageCategoryVerify"}, {"name": "host", "value": "SHLAP10427"}, {"name": "thread", "value": "10640-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "src.Mweb.EC.testcases.mweb_social.test_112741_h5_social_search_accounts_verify"}]}