import allure
import pytest

from playwright.sync_api import Page

from src.Mweb.EC.mweb_ele.mweb_product import mweb_pdp_ele
from src.Mweb.EC.mweb_pages.mweb_page_pdp.mweb_page_pdp import MWebPDPPage
from src.common.commonui import scroll_one_page_until


@allure.story("【108209】 PDP-分享-商品分享流程验证")
class TestMWebPDPProductShareUIUX:
    pytestmark = [pytest.mark.h5pdp, pytest.mark.mweb_regression, pytest.mark.transaction, pytest.mark.suqin]

    @allure.title("【108209】 PDP-分享-商品分享流程验证")
    def test_108209_mWeb_pdp_product_share_ui_ux(self, phone_page: dict, h5_autotest_header, h5_open_and_close_trace):
        """
        【108209】 PDP-分享-商品分享流程验证
        """
        p: Page = phone_page.get("page")
        c = phone_page.get("context")
        # 直接进入指定pdp页面
        pdp_page = MWebPDPPage(p, h5_autotest_header, browser_context=c,
                               page_url="/product/Vita-Honey-Chrysanthemum-Tea-250ml-6/106422")
        p.wait_for_timeout(3000)
        # 关闭Continue pop
        if p.locator(u"//button[contains(text(), 'Continue')]").all():
            p.locator(u"//button[contains(text(), 'Continue')]").click()
        # 滚动到指定位置-分享按钮
        scroll_one_page_until(p, mweb_pdp_ele.ele_share)
        # 点击分享按钮
        pdp_page.FE.ele(mweb_pdp_ele.ele_share).click()
        p.wait_for_timeout(3000)
        # 断言分享pop 弹出成功
        assert pdp_page.FE.ele(mweb_pdp_ele.ele_share_pop).is_visible(), 'pdp点击分享未弹出pop'
        # 断言分享pop title
        assert pdp_page.FE.ele(mweb_pdp_ele.ele_share_pop_title).text_content() == "Share"
        # 断言分享商品小图
        assert pdp_page.FE.ele(mweb_pdp_ele.ele_share_pop_product_img + "//img").is_visible()
        # 断言分享pop 商品title
        pop_product_title = pdp_page.FE.ele(
            mweb_pdp_ele.ele_share_pop_product_img + "//following-sibling::div//div[1]").text_content()
        assert pop_product_title
        # 断言分享pop 商品子title
        pop_product_sub_title = pdp_page.FE.ele(
            mweb_pdp_ele.ele_share_pop_product_img + "//following-sibling::div//div[2]").text_content()
        assert pop_product_sub_title == "Top choice for Asian groceries."
        # 断言分享语言

        share_lang_text = p.locator(mweb_pdp_ele.ele_share_pop_product_lan).all_text_contents()
        assert {"English", "简", "繁", "한국어", "日本語", "Tiếng Việt"} == set(share_lang_text)
        # 切换分享语言
        share_lang = pdp_page.FE.eles(mweb_pdp_ele.ele_share_pop_product_lan)
        for item in share_lang:
            # 切换语言
            item.click()

        # 断言分享返利文案

        # 断言分享方式
        assert pdp_page.FE.ele(mweb_pdp_ele.ele_share_pop_copy_link).is_visible()
        assert pdp_page.FE.ele(mweb_pdp_ele.ele_share_pop_copy_link + "//span").text_content() == "Copy link"

        # 点击copy link
        pdp_page.FE.ele(mweb_pdp_ele.ele_share_pop_copy_link).click()
        # 断言pop 关闭
        assert not pdp_page.FE.ele(mweb_pdp_ele.ele_share_pop)
