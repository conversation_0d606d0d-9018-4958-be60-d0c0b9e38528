"""
<AUTHOR>  li.zhu
@Version        :  V1.0.0
------------------------------------
@File           :   test_112913_mweb_checkout_single_ui_ux.py
@Description    :  
@CreateTime     :  2025/3/25 13:59
@Software       :  PyCharm
------------------------------------
@ModifyTime     :  2025/3/25 13:59
"""
from src.Mweb.EC.mweb_ele.mweb_cart import mweb_cart_ele
from src.Mweb.EC.mweb_pages.mweb_page_cart.mweb_page_cart import MWebCartPage
from src.Mweb.EC.mweb_pages.mweb_page_category.mweb_page_category import MWebCategorypage
from src.common.commonui import scroll_one_page_until

"""
<AUTHOR>  [your_name]
@Version        :  V1.0.0
------------------------------------
@File           :   test_h5_checkout_credit_card.py
@Description    :   H5加购商品到信用卡支付流程
@CreateTime     :  2024/3/21
------------------------------------
"""
import allure
import pytest
from playwright.sync_api import Page, expect
from src.config.weee.log_help import log
from src.common.commfunc import empty_cart


@allure.epic("H5结算流程测试")
@allure.feature("信用卡支付流程")
class TestCheckoutSingleUiUx:

    @allure.title("H5结算生鲜订单")
    @pytest.mark.smoke
    def test_112913_checkout_single_ui_ux(self, phone_page: dict, h5_autotest_header):
        """
        测试步骤：
        1. 清空购物车
        2. 从分类页加购商品
        3. 进入购物车
        4. 点击结算按钮
        5. 选择配送地址
        6. 选择配送时间
        7. 选择信用卡支付
        8. 提交订单
        """
        p: Page = phone_page.get("page")
        c = phone_page.get("context")
        # 初始化页面对象
        cart_page = MWebCartPage(p, h5_autotest_header, browser_context=c, page_url="/cart")
        with allure.step("清空购物车"):
            try:
                empty_cart(h5_autotest_header)
                p.reload()
                p.wait_for_timeout(2000)
                log.info("购物车清空成功")
            except Exception as e:
                log.error(f"清空购物车失败: {str(e)}")
                raise

        # 滚动到指定位置-猜你喜欢
        scroll_one_page_until(p, mweb_cart_ele.ele_recommend_tab)
        p.wait_for_timeout(2000)
        # 获取猜你喜欢商品
        recommend_card = cart_page.FE.eles(mweb_cart_ele.ele_recommend_module_card)
        for index1, item1 in enumerate(recommend_card):
            # 加购推荐商品
            item1.query_selector("//div[@data-testid='btn-atc-plus']").click()
            # item1.query_selector(mweb_cart_ele.ele_cart_atc_normal_plus).click()
            p.wait_for_timeout(1000)
            if index1 == 2:
                break

        # with allure.step("从分类页加购商品"):
        #     try:
        #         # 进入分类页
        #         category_page = MWebCategorypage(p, h5_autotest_header, browser_context=c)
        #         # 添加商品到购物车
        #         category_page.add_to_local_product_cart_from_category()
        #         p.wait_for_timeout(2000)
        #         log.info("商品加购成功")
        #     except Exception as e:
        #         log.error(f"加购商品失败: {str(e)}")
        #         raise

        with allure.step("点击结算按钮"):
            try:
                checkout_button = p.get_by_test_id("btn-checkout")
                expect(checkout_button).to_be_visible()
                checkout_button.click()
                p.wait_for_timeout(2000)
                log.info("成功点击结算按钮")
            except Exception as e:
                log.error(f"点击结算按钮失败: {str(e)}")
                raise

        # with allure.step("选择配送地址"):
        #     try:
        #         # 点击地址选择器
        #         address_selector = p.get_by_test_id("wid-checkout-address-selector")
        #         address_selector.click()
        #         p.wait_for_timeout(1000)
        #
        #         # 选择第一个地址
        #         first_address = p.get_by_test_id("wid-checkout-address-item").first
        #         first_address.click()
        #         p.wait_for_timeout(1000)
        #         log.info("配送地址选择成功")
        #     except Exception as e:
        #         log.error(f"选择配送地址失败: {str(e)}")
        #         raise
        #
        # with allure.step("选择配送时间"):
        #     try:
        #         # 点击配送时间选择器
        #         time_selector = p.get_by_test_id("wid-checkout-delivery-time-selector")
        #         time_selector.click()
        #         p.wait_for_timeout(1000)
        #
        #         # 选择第一个可用时间
        #         first_time_slot = p.get_by_test_id("wid-checkout-time-slot").first
        #         first_time_slot.click()
        #         p.wait_for_timeout(1000)
        #         log.info("配送时间选择成功")
        #     except Exception as e:
        #         log.error(f"选择配送时间失败: {str(e)}")
        #         raise
        #
        # with allure.step("选择信用卡支付"):
        #     try:
        #         # 点击支付方式选择器
        #         payment_selector = p.get_by_test_id("wid-checkout-payment-selector")
        #         payment_selector.click()
        #         p.wait_for_timeout(1000)
        #
        #         # 选择信用卡支付
        #         credit_card_option = p.get_by_test_id("wid-checkout-credit-card-option")
        #         credit_card_option.click()
        #         p.wait_for_timeout(1000)
        #         log.info("信用卡支付方式选择成功")
        #     except Exception as e:
        #         log.error(f"选择支付方式失败: {str(e)}")
        #         raise
        #
        # with allure.step("提交订单"):
        #     try:
        #         # 点击提交订单按钮
        #         submit_button = p.get_by_test_id("wid-checkout-submit-order")
        #         expect(submit_button).to_be_enabled()
        #         submit_button.click()
        #         p.wait_for_timeout(3000)
        #
        #         # 验证订单提交成功
        #         success_message = p.get_by_test_id("wid-order-success-message")
        #         expect(success_message).to_be_visible()
        #         log.info("订单提交成功")
        #     except Exception as e:
        #         log.error(f"提交订单失败: {str(e)}")
        #         raise
        #
        # p.wait_for_timeout(2000)

