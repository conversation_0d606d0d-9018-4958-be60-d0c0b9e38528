{"name": "H5-首页分类验证", "status": "failed", "statusDetails": {"message": "AssertionError: <Locator frame=<Frame name= url='https://www.sayweee.com/en/cms/page/activity/iconevent-AAPI'> selector=\"a[data-testid*='wid-categories-item'] >> nth=2\">分类下没有商品\nassert []\n +  where [] = <bound method Locator.all of <Locator frame=<Frame name= url='https://www.sayweee.com/en/cms/page/activity/iconevent-AAPI'> selector='internal:testid=[data-testid=\"wid-product-card-container\"s]'>>()\n +    where <bound method Locator.all of <Locator frame=<Frame name= url='https://www.sayweee.com/en/cms/page/activity/iconevent-AAPI'> selector='internal:testid=[data-testid=\"wid-product-card-container\"s]'>> = <Locator frame=<Frame name= url='https://www.sayweee.com/en/cms/page/activity/iconevent-AAPI'> selector='internal:testid=[data-testid=\"wid-product-card-container\"s]'>.all\n +      where <Locator frame=<Frame name= url='https://www.sayweee.com/en/cms/page/activity/iconevent-AAPI'> selector='internal:testid=[data-testid=\"wid-product-card-container\"s]'> = <bound method Page.get_by_test_id of <Page url='https://www.sayweee.com/en/cms/page/activity/iconevent-AAPI'>>('wid-product-card-container')\n +        where <bound method Page.get_by_test_id of <Page url='https://www.sayweee.com/en/cms/page/activity/iconevent-AAPI'>> = <Page url='https://www.sayweee.com/en/cms/page/activity/iconevent-AAPI'>.get_by_test_id", "trace": "self = <src.Mweb.EC.testcases.mweb_home.test_112811_h5_home_page_category_verify.TestH5HomePageCategoryVerify object at 0x000001976A5B53D0>\nphone_page = {'context': <BrowserContext browser=<Browser type=<BrowserType name=chromium executable_path=C:\\Users\\<USER>\\App...\\chrome.exe> version=120.0.6099.28>>, 'page': <Page url='https://www.sayweee.com/en/cms/page/activity/iconevent-AAPI'>}\nh5_autotest_header = {'Content-Type': 'application/json;charset=UTF-8', 'Zipcode': '98011', 'app-version': 'null', 'authorization': 'Bearer...NkwMMCev6GKCAKMwzMQSu_gp1a3H3YKKN0Zfx8aodjtSl704646Gq87ziAmnx19wB3ceU697lTgwW8_63hsiZhM8sba-33Zxx4VAEuU6XV2Cp9vk', ...}\nh5_open_and_close_trace = None\n\n    @allure.title(\"H5-首页分类验证\")\n    @pytest.mark.h5home\n    def test_112811_h5_home_page_category_verify(self, phone_page: dict, h5_autotest_header, h5_open_and_close_trace):\n        _page:Page = phone_page[\"page\"]\n        _context = phone_page[\"context\"]\n        ph5 = PH5(_page, h5_autotest_header, _context)\n        # 首页category存在\n        assert _page.get_by_test_id(\"wid-home-collection-cm_categories\").is_visible()\n        # 断言每行显示5个分类, 只能断言10个，页面上布局代码未分2个div\n        # assert len(_page.get_by_test_id(\"wid-home-collection-cm_categories\").locator(\"//./div[contains(@class, 'grid')]/div\").all()) == 10\n        assert len(_page.get_by_test_id(\"wid-home-collection-cm_categories\").locator(\"xpath=/div[contains(@class, 'grid')]/div\").all()) == 10\n        # 点击...拉起弹框\n        _page.get_by_test_id(\"wid-view-more\").click()\n        _page.wait_for_timeout(3000)\n        assert _page.locator(\"#popup-categories-header\").is_visible()\n        assert _page.locator(\"#popup-categories-header\").locator(\"xpath=//h2[text()='Categories']\").is_visible()\n        # 判断弹框内的分类数量大于12\n        assert len(_page.get_by_test_id(\"wid-categories-item\").all()) > 12\n        # 回到首页\n        _page.get_by_test_id(\"btn-modal-close\").click()\n        # 点击每个分类\n        all_categories = _page.locator(\"a[data-testid*='wid-categories-item']\").all()\n        for category in all_categories:\n            log.info(\"category is: \" + category.get_attribute(\"aria-label\"))\n            category_aria_label = category.get_attribute(\"aria-label\")\n            category.click()\n            _page.wait_for_timeout(3000)\n            # global+首页不滚动，没有商品显示\n            if \"Global+\" not in category_aria_label:\n>               assert _page.get_by_test_id(\"wid-product-card-container\").all(), f\"{category}分类下没有商品\"\nE               AssertionError: <Locator frame=<Frame name= url='https://www.sayweee.com/en/cms/page/activity/iconevent-AAPI'> selector=\"a[data-testid*='wid-categories-item'] >> nth=2\">分类下没有商品\nE               assert []\nE                +  where [] = <bound method Locator.all of <Locator frame=<Frame name= url='https://www.sayweee.com/en/cms/page/activity/iconevent-AAPI'> selector='internal:testid=[data-testid=\"wid-product-card-container\"s]'>>()\nE                +    where <bound method Locator.all of <Locator frame=<Frame name= url='https://www.sayweee.com/en/cms/page/activity/iconevent-AAPI'> selector='internal:testid=[data-testid=\"wid-product-card-container\"s]'>> = <Locator frame=<Frame name= url='https://www.sayweee.com/en/cms/page/activity/iconevent-AAPI'> selector='internal:testid=[data-testid=\"wid-product-card-container\"s]'>.all\nE                +      where <Locator frame=<Frame name= url='https://www.sayweee.com/en/cms/page/activity/iconevent-AAPI'> selector='internal:testid=[data-testid=\"wid-product-card-container\"s]'> = <bound method Page.get_by_test_id of <Page url='https://www.sayweee.com/en/cms/page/activity/iconevent-AAPI'>>('wid-product-card-container')\nE                +        where <bound method Page.get_by_test_id of <Page url='https://www.sayweee.com/en/cms/page/activity/iconevent-AAPI'>> = <Page url='https://www.sayweee.com/en/cms/page/activity/iconevent-AAPI'>.get_by_test_id\n\ntest_112811_h5_home_page_category_verify.py:41: AssertionError"}, "start": 1746525997896, "stop": 1746526037111, "uuid": "90d51fa3-5bd1-460c-bbf9-7d33e205c945", "historyId": "f63eb5d3679fa3c877592007134945db", "testCaseId": "f63eb5d3679fa3c877592007134945db", "fullName": "src.Mweb.EC.testcases.mweb_home.test_112811_h5_home_page_category_verify.TestH5HomePageCategoryVerify#test_112811_h5_home_page_category_verify", "labels": [{"name": "story", "value": "H5-首页分类验证"}, {"name": "tag", "value": "h5home"}, {"name": "tag", "value": "case"}, {"name": "tag", "value": "test"}, {"name": "tag", "value": "mweb_regression"}, {"name": "parentSuite", "value": "src.Mweb.EC.testcases.mweb_home"}, {"name": "suite", "value": "test_112811_h5_home_page_category_verify"}, {"name": "subSuite", "value": "TestH5HomePageCategoryVerify"}, {"name": "host", "value": "SHLAP10427"}, {"name": "thread", "value": "26540-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "src.Mweb.EC.testcases.mweb_home.test_112811_h5_home_page_category_verify"}]}