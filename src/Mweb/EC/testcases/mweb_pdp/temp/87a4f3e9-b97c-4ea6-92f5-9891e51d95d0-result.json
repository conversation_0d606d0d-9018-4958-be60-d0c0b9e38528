{"name": "【112063】 H5-PDP-商品组-只有一个属性模块UI/UX验证", "status": "passed", "description": "\n        【112063】 H5-PDP-商品组-只有一个属性模块UI/UX验证\n        ", "start": 1743404689489, "stop": 1743404708916, "uuid": "f5d215a3-d376-4b32-bc38-c751874a29d2", "historyId": "ca2a46b49d3680d87bae02feff8234ca", "testCaseId": "ca2a46b49d3680d87bae02feff8234ca", "fullName": "src.Mweb.EC.testcases.mweb_pdp.test_112063_mWeb_pdp_product_group_ui_ux.TestMWebPDPProductGroupUIUX#test_112063_mWeb_pdp_product_image_group_ui_ux", "labels": [{"name": "story", "value": "【112063】 H5-PDP-商品组-只有一个属性模块UI/UX验证"}, {"name": "tag", "value": "case"}, {"name": "tag", "value": "test"}, {"name": "tag", "value": "h5pdp"}, {"name": "tag", "value": "mweb_regression"}, {"name": "tag", "value": "transaction"}, {"name": "tag", "value": "suqin"}, {"name": "parentSuite", "value": "src.Mweb.EC.testcases.mweb_pdp"}, {"name": "suite", "value": "test_112063_mWeb_pdp_product_group_ui_ux"}, {"name": "subSuite", "value": "TestMWebPDPProductGroupUIUX"}, {"name": "host", "value": "SHLAP10427"}, {"name": "thread", "value": "10200-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "src.Mweb.EC.testcases.mweb_pdp.test_112063_mWeb_pdp_product_group_ui_ux"}]}