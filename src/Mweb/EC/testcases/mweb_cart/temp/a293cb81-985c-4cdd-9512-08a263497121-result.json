{"name": "H5生鲜购物车UI自动化验证", "status": "passed", "description": "\n        生鲜购物车UI自动化验证:\n        1. 调用切换zipcode接口切换到98011\n        2. 加购为你推荐商品\n        3. 遍历购物车商品\n        4. 获取购物车商品标题和价格(带$符号)\n        ", "start": 1745299217714, "stop": 1745299432143, "uuid": "37e78ec1-8a7f-4ac6-b2db-0a0141cdbace", "testCaseId": "391c802eeed8ca0da36b84e4b17b1a38", "fullName": "src.Mweb.EC.testcases.mweb_cart.test_mweb_grocery_cart_ui_ux.TestMwebGroceryCartUIUX#test_fresh_grocery_cart_ui"}