{"name": "H5生鲜购物车UI自动化验证", "status": "failed", "statusDetails": {"message": "AssertionError: 商品 3 价格 $1.99$2.99 格式不正确\nassert None\n +  where None = <function match at 0x000001E7167211C0>('^\\\\$\\\\d+\\\\.\\\\d{2}$', '$1.99$2.99')\n +    where <function match at 0x000001E7167211C0> = re.match", "trace": "self = <src.Mweb.EC.testcases.mweb_cart.test_mweb_grocery_cart_ui_ux.TestMwebGroceryCartUIUX object at 0x000001E71CC27790>\nphone_page = {'context': <BrowserContext browser=<Browser type=<BrowserType name=chromium executable_path=C:\\Users\\<USER>\\App...091\\chrome-win\\chrome.exe> version=120.0.6099.28>>, 'page': <Page url='https://www.sayweee.com/en/cart?joinEnki=true'>}\nh5_autotest_header = {'Content-Type': 'application/json;charset=UTF-8', 'Zipcode': '98011', 'app-version': 'null', 'authorization': 'Bearer...tW4ZlKVxyyPOlLKcjuw20dNE9D_aMsc8MJw4M370nBZWFTTtrUKR8UUz0m_nd36uEgpZigLs8o1jwoGLtM8dcJWeTUDfZUvj3jGE7EUIqiCZWLZY', ...}\nh5_open_and_close_trace = None\n\n    @allure.title(\"H5生鲜购物车UI自动化验证\")\n    @pytest.mark.fresh\n    def test_fresh_grocery_cart_ui(self, phone_page: dict, h5_autotest_header, h5_open_and_close_trace):\n        \"\"\"\n        生鲜购物车UI自动化验证:\n        1. 调用切换zipcode接口切换到98011\n        2. 加购为你推荐商品\n        3. 遍历购物车商品\n        4. 获取购物车商品标题和价格(带$符号)\n        \"\"\"\n        p: Page = phone_page.get(\"page\")\n        c = phone_page.get(\"context\")\n    \n        # 1. 调用切换zipcode接口切换到98011\n        log.info(\"切换zipcode到98011\")\n        res = switch_zipcode(h5_autotest_header, \"98011\")\n        assert res.get('object') == 'Success', f\"切换zipcode失败，res={res}\"\n        p.wait_for_timeout(2000)\n    \n        # 进入购物车页面\n        cart_page = MWebCartPage(p, h5_autotest_header, browser_context=c, page_url=\"/cart\")\n        p.wait_for_timeout(2000)\n    \n        # 关闭可能出现的广告弹窗\n        if p.locator(\"//img[contains(@aria-label, 'close button')]\").all():\n            p.locator(\"//img[contains(@aria-label, 'close button')]\").click()\n            log.info(\"关闭广告弹窗\")\n    \n        # 2. 滚动到推荐商品区域\n        log.info(\"滚动到推荐商品区域\")\n        scroll_one_page_until(p, mweb_cart_ele.ele_recommend_tab)\n        p.wait_for_timeout(1000)\n    \n        # 获取推荐商品列表\n        recommend_card = cart_page.FE.eles(mweb_cart_ele.ele_recommend_module_card)\n        added_count = 0\n    \n        # 加购推荐商品\n        log.info(\"开始加购推荐商品\")\n        for index, item in enumerate(recommend_card):\n            plus_btn = item.query_selector(\"[data-testid='btn-atc-plus']\")\n            if not plus_btn:\n                # 备用定位方式\n                plus_btn = item.query_selector(mweb_cart_ele.ele_cart_atc_normal_plus)\n    \n            if plus_btn and plus_btn.is_visible():\n                plus_btn.click()\n                p.wait_for_timeout(1000)\n                added_count += 1\n                log.info(f\"成功添加第 {added_count} 个推荐商品\")\n    \n                # 添加2个商品后停止\n                if added_count >= 2:\n                    break\n    \n    \n        assert added_count > 0, \"没有成功添加商品到购物车\"\n    \n        # 等待页面加载完成\n        p.wait_for_timeout(2000)\n    \n        # 3. 滚动回购物车顶部\n        log.info(\"滚动回购物车顶部\")\n        scroll_one_page_until(p, mweb_cart_ele.ele_cart_normal_card)\n        p.wait_for_timeout(2000)\n    \n        # 4. 遍历购物车商品，获取标题和价格\n        # 使用test_id定位购物车商品\n        cart_items = p.locator(\"[data-testid='cart-normal-card']\").all()\n        if not cart_items:\n            # 备用定位方式\n            cart_items = cart_page.FE.eles(mweb_cart_ele.ele_cart_normal_card)\n    \n        assert len(cart_items) > 0, \"购物车中没有商品\"\n        log.info(f\"购物车中共有 {len(cart_items)} 个商品\")\n    \n        # 遍历购物车商品\n        for index, item in enumerate(cart_items):\n            # 获取商品标题\n            title_element = item.query_selector(\"//div[@data-testid='wid-product-card-title']\")\n    \n            assert title_element, f\"未找到第 {index + 1} 个商品的标题元素\"\n            title = title_element.text_content().strip()\n            log.info(f\"商品 {index + 1} 标题: {title}\")\n            assert len(title) > 0, f\"商品 {index + 1} 标题为空\"\n    \n            # 获取商品价格\n            price_element = item.query_selector(\"div[data-testid='wid-product-card-price']\")\n            assert price_element, f\"未找到第 {index + 1} 个商品的价格元素\"\n            price = price_element.text_content().strip()\n            log.info(f\"商品 {index + 1} 价格: {price}\")\n    \n            # 验证价格包含$符号\n            assert \"$\" in price, f\"商品 {index + 1} 价格 {price} 不包含 $ 符号\"\n    \n            # 验证价格格式（$xx.xx）\n            price_pattern = r'^\\$\\d+\\.\\d{2}$'\n>           assert re.match(price_pattern, price), f\"商品 {index + 1} 价格 {price} 格式不正确\"\nE           AssertionError: 商品 3 价格 $1.99$2.99 格式不正确\nE           assert None\nE            +  where None = <function match at 0x000001E7167211C0>('^\\\\$\\\\d+\\\\.\\\\d{2}$', '$1.99$2.99')\nE            +    where <function match at 0x000001E7167211C0> = re.match\n\ntest_mweb_grocery_cart_ui_ux.py:126: AssertionError"}, "description": "\n        生鲜购物车UI自动化验证:\n        1. 调用切换zipcode接口切换到98011\n        2. 加购为你推荐商品\n        3. 遍历购物车商品\n        4. 获取购物车商品标题和价格(带$符号)\n        ", "start": 1745300519837, "stop": 1745300707735, "uuid": "d52244b3-1c38-45a7-9f6b-************", "historyId": "391c802eeed8ca0da36b84e4b17b1a38", "testCaseId": "391c802eeed8ca0da36b84e4b17b1a38", "fullName": "src.Mweb.EC.testcases.mweb_cart.test_mweb_grocery_cart_ui_ux.TestMwebGroceryCartUIUX#test_fresh_grocery_cart_ui", "labels": [{"name": "story", "value": "H5购物车-生鲜购物车UI/UX验证"}, {"name": "tag", "value": "fresh"}, {"name": "tag", "value": "case"}, {"name": "tag", "value": "test"}, {"name": "tag", "value": "mweb_regression"}, {"name": "tag", "value": "<PERSON><PERSON><PERSON>"}, {"name": "parentSuite", "value": "src.Mweb.EC.testcases.mweb_cart"}, {"name": "suite", "value": "test_mweb_grocery_cart_ui_ux"}, {"name": "subSuite", "value": "TestMwebGroceryCartUIUX"}, {"name": "host", "value": "SHLAP10427"}, {"name": "thread", "value": "33024-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "src.Mweb.EC.testcases.mweb_cart.test_mweb_grocery_cart_ui_ux"}]}