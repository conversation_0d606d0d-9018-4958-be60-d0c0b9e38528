import allure
import pytest

from playwright.sync_api import Page

from src.Mweb.EC.mweb_ele.mweb_cart import mweb_cart_ele
from src.Mweb.EC.mweb_pages.mweb_page_cart.mweb_page_cart import MWebCartPage
from src.config.weee.log_help import log
from src.common.commfunc import empty_cart


@allure.story("【113360】 H5购物车-空购物车UI/UX验证")
class TestMWebEmptyCartUIUX:
    pytestmark = [pytest.mark.h5cart, pytest.mark.regression, pytest.mark.transaction, pytest.mark.suqin]

    @allure.title("【113360】 H5购物车-空购物车UI/UX验证")
    def test_113360_mWeb_empty_cart_ui_ux(self, phone_page: dict, h5_autotest_header, h5_open_and_close_trace):
        """
        【113360】 H5购物车-空购物车UI/UX验证
        """
        p: Page = phone_page.get("page")
        c = phone_page.get("context")
        try:
            empty_cart(h5_autotest_header)
        except Exception as e:
            log.info("清空购物车发生异常" + str(e))
        cart_page = MWebCartPage(p, h5_autotest_header, browser_context=c, page_url="/cart")
        p.reload()
        p.wait_for_timeout(5000)
        # 断言购物车顶部文案
        cart_title = cart_page.FE.ele(mweb_cart_ele.ele_cart_title).text_content()
        assert cart_title == "My cart"
        # 断言空购物车img存在
        assert cart_page.FE.ele(mweb_cart_ele.ele_empty_cart_img).is_visible()
        # 断言空购物车 文案存在
        cart_text = cart_page.FE.ele(mweb_cart_ele.ele_empty_cart_text).text_content()
        assert cart_text == "Your cart is empty"

        # 往下滑动,推荐组件置顶
        p.evaluate('window.scrollTo(0, document.body.scrollHeight/2)')

        # 断言header 悬浮
        assert cart_page.FE.ele(u"//div[@id='layout-header' and contains(@style,'box-shadow')]")

        assert cart_page.FE.ele(mweb_cart_ele.ele_recommend_tab).text_content() == "Recommendations"
        # 点击一键置顶
        p.get_by_test_id("btn-back-to-top")
        # 点击空购物车的start_shopping按钮
        cart_page.mweb_start_shopping()
        p.wait_for_timeout(5000)
        # 断言跳转到了首页,判断能找到首页banner即可
        assert p.get_by_test_id("mod-main-banner").is_visible()
