import allure
import pytest

from src.Mweb.EC.mweb_pages.mweb_page_cart.mweb_page_cart import MWebCartPage as PH5C
from playwright.sync_api import Page


@allure.story("H5购物车操作")
class TestH5CartPage:
    # @pytest.mark.repeat(10)
    @allure.title("H5空购物车操作")
    @pytest.mark.h5cart
    @pytest.mark.h5smoke
    def test_mweb_cart_page_operations(self, phone_page: dict, h5_autotest_header, h5_open_and_close_trace):
        """购物车没有商品"""
        p: Page = phone_page.get("page")
        c = phone_page.get("context")
        ph5c = PH5C(phone_page["page"], h5_autotest_header, browser_context=c)
        ph5c.cart_page_operations()

    def test_mweb_checkout_with_paypal_check(self, phone_page: dict, h5_autotest_header):
        p: Page = phone_page.get("page")
        c = phone_page.get("context")
        ph5c = PH5C(phone_page["page"], h5_autotest_header, browser_context=c)
        ph5c.checkout_with_paypal()
