import asyncio

from src.config.weee.mysqlUtil import MysqlUtil


async def tb1_mysql_query(sql, conn):
    res = await conn.execute_query(sql_str=sql)
    print("res===>", res)
    return res


async def tb1_mysql_update(sql, conn):
    res = await conn.execute_update(sql_str=sql)
    print("res===>", res)
    return res


if __name__ == '__main__':
    # cn = asyncio.run(tb1_mysql_query('select * from sys_user'))
    # sql = """ insert into sys_user (dept_id, user_name, nick_name, user_type, email, phonenumber, sex, avatar, password,
    #                   status, del_flag, login_ip, login_date, create_by, create_time, update_by, update_time, remark)
    #         values (105, 'aaa', 'bbb', '00', '<EMAIL>', '177777', '0', '', '777', '0', '0', '127.0.0.1', '2024-02-04 11:24:20', 'admin', '2024-02-04 10:50:10', '', '2024-02-06 16:09:55', '管理员') """
    # cu = asyncio.run(tb1_mysql_update(sql=sql))
    sql2 = 'select * from sys_user'

    with MysqlUtil(host='***************', user='root', password='Hello!234', db='ry-vue') as conn:
        asyncio.run(tb1_mysql_update(sql=sql2, conn=conn))

