import os

# BASE_URL = "https://api.tb1.sayweee.net" if not os.getenv("base_url", None) else os.getenv("base_url")
# TEST_URL = "https://tb1.sayweee.net/en" if not os.getenv("test_url", None) else os.getenv("test_url")

BASE_URL = 'https://api.sayweee.net' if not os.getenv("base_url", None) else os.getenv("base_url")
TEST_URL = "https://www.sayweee.com/en" if not os.getenv("test_url", None) else os.getenv("test_url")

header_init = {
        # "accept": "application/json, text/plain, */*",
        "Content-Type": "application/json;charset=UTF-8",
        "app-version": "null",
        "authorization": "",
        "b-cookie": "",
        "lang": "en",
        "platform": "h5",
        "user-agent": "WeeeA<PERSON>",
        # "user-agent": "Mozilla/5.0 (iPhone; CPU iPhone OS 13_2_3 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/13.0.3 Mobile/15E148 Safari/604.1",
        "weee-session-token": "",
        "weee-store": "cn",
        "Zipcode": "98011"
    }