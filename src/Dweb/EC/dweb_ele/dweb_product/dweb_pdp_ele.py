# 1. add to cart按钮
ele_pdp_add_to_cart = "//div[text()='Add to cart']"
ele_pdp_add_to_cart_attrs = "//div[text()='Add to cart']/attribute::*"

# 2. 缩略图
ele_pdp_thumbnail = "div[class^='Header_thumbnail']"
# 3. 主图
ele_pdp_primary_img = "div[class^='Header_atlas']"

# 右侧信息区 Header_content
ele_pdp_header_content = "div[class^='Header_content']"

# related products div
ele_pdp_related = 'div[data-role="related"]'
# pdp 相关商品加购按钮
ele_pdp_related_add_to_cart_button = "div[data-role='related'] i[data-role='addButtonPlusIcon']"
# pdp product group 模块
ele_product_group = u"//div[contains(@class,'Variation_variation')]"
ele_product_group_title = ele_product_group+u"//div[contains(@class,'property_title')]"
ele_product_group_item_list = ele_product_group+u"//div[contains(@class,'property_listItem')]"


# pdp review 模块
ele_mod_review = u"//div[@data-testid='mod-reviews']"
ele_mod_review_card = ele_mod_review+u"//a[@data-testid='wid-review-card']"
# pdp review 片弹出pop
ele_review_pop = u"//div[@data-type='popup']"
ele_review_pop_review_list = ele_review_pop+u"//div[@data-testid='wid-review-list']"

# pdp review pop item list
ele_review_pop_item_list = ele_review_pop + u"//div[@data-testid='wid-review-item']"
# review pop 右上角x按钮
ele_review_pop_close_button = u"//div[@data-type='popup']//div[@data-testid='btn-modal-close']"


# pdp 视频模块
ele_mod_videos = u"//div[@data-testid='mod-videos']"
# 视频卡片
ele_video_card = u"//div[@data-testid='wid-video-card']"
# 视频卡片下方文案
ele_video_card_title = u"//div[@data-testid='text-video-card-title']"
# 视频下方头像
ele_video_card_avatar = u"//div[@data-testid='wid-video-card-avatar']"
# 视频下方点赞按钮
ele_video_card_like = u"//div[@data-testid='wid-set-like']"
# 视频卡片弹出pop
ele_video_pop = u"//div[@data-type='popup']//div[@data-testid='mod-video-modal']"
# 视频卡片弹出pop 右上角x按钮
video_pop_close_button = ele_video_pop + u"//div[@data-testid='btn-modal-close']"
# 视频pop的左侧视频模块
video_pop_video = u"//div[contains(@class,'video_video-box')]"
# 视频pop的左侧视频里的图片
video_pop_img = video_pop_video + u"//div[contains(@class,'video_video-box')]//img[@src]"
# 视频pop的左侧视频里的播放按钮
video_pop_play_icon = u"//div[contains(@class,'video_video-box')]//div[@data-type='play-icon']"
# 视频右侧评论输入框的placeholder
video_pop_comment = u"//div[@data-testid='wid-comments-input']"
# 视频右侧评论输入框 post 按钮
video_pop_post = u"//button[@data-testid='btn-comments-post']"
# 视频右侧评论输入框下方评论 模块
video_pop_comment_item = u"//button[@data-testid='wid-comment-item']"
# 视频右侧商品卡片模块
video_pop_product_card = u"//button[@data-testid='wid-product-card']"
