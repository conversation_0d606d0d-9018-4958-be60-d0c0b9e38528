{"name": "WEB-注册-onboarding-页面UI/UX验证", "status": "broken", "statusDetails": {"message": "playwright._impl._errors.TimeoutError: Timeout 30000ms exceeded.", "trace": "self = <src.Dweb.EC.testcases.dweb_home.test_102480_web_signup_onboarding_ui_ux.TestWebSignupOnboardingUIUX object at 0x00000212E30ED050>\npage = {'context': <BrowserContext browser=<Browser type=<BrowserType name=chromium executable_path=C:\\Users\\<USER>\\App...\\chrome-win\\chrome.exe> version=120.0.6099.28>>, 'page': <Page url='https://www.sayweee.com/en?grocery-store=chinese'>}\npc_autotest_header = {'Content-Type': 'application/json;charset=UTF-8', 'Zipcode': '98011', 'app-version': 'null', 'authorization': 'Bearer...dbJwWtMg1L-BBSyqpmt3_gisGpL6AZxrl8MYYnxFOJC8fF0lHI_mvCp5zg3zsAUtmOV8oi5BwB01Fc0CM3HeWxrD3Wae950JZzv_uaUi03ALG_Zc', ...}\nlogin_trace = None\n\n    @allure.title(\"WEB-注册-onboarding-页面UI/UX验证\")\n    def test_102480_web_signup_onboarding_ui_ux(self, page: dict, pc_autotest_header,login_trace):\n        \"\"\"\n        【102480】 WEB-注册-onboarding-页面UI/UX验证\n        \"\"\"\n        p:Page = page.get(\"page\")\n        c = page.get(\"context\")\n        home_page = DWebHomePage(p, pc_autotest_header, browser_context=c)\n        # 1. 关闭time banner\n        home_page.close_time_banner()\n        # 断言time banner不存在\n        assert not p.locator(\"#timeBanner img\").is_visible()\n        # 2. 切换store到Chinese\n        home_page.home_switch_specific_store(\"Chinese\")\n        # 3. 点击底部各种城市\n        # 3.1 要滚动到底部，否则无法点击城市，此时各城市element未加载\n        scroll_one_page_until(p, \"div[class*='Footer_ft_locationsWrapper']\")\n        _new_page = home_page.click_all_cities_on_bottom_home_page(\"SF Bay Area\")\n>       _new_page.wait_for_selector(\"//img[@class='logo']\")\n\ntest_102480_web_signup_onboarding_ui_ux.py:32: \n_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _\nD:\\qa-ui-dmweb\\vevn-dmweb\\Lib\\site-packages\\playwright\\sync_api\\_generated.py:8334: in wait_for_selector\n    self._sync(\nD:\\qa-ui-dmweb\\vevn-dmweb\\Lib\\site-packages\\playwright\\_impl\\_page.py:348: in wait_for_selector\n    return await self._main_frame.wait_for_selector(**locals_to_params(locals()))\nD:\\qa-ui-dmweb\\vevn-dmweb\\Lib\\site-packages\\playwright\\_impl\\_frame.py:317: in wait_for_selector\n    await self._channel.send(\"waitForSelector\", locals_to_params(locals()))\nD:\\qa-ui-dmweb\\vevn-dmweb\\Lib\\site-packages\\playwright\\_impl\\_connection.py:62: in send\n    return await self._connection.wrap_api_call(\nD:\\qa-ui-dmweb\\vevn-dmweb\\Lib\\site-packages\\playwright\\_impl\\_connection.py:492: in wrap_api_call\n    return await cb()\n_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _\n\nself = <playwright._impl._connection.Channel object at 0x00000212E37D2950>\nmethod = 'waitForSelector', params = {'selector': \"//img[@class='logo']\"}\nreturn_as_dict = False\n\n    async def inner_send(\n        self, method: str, params: Optional[Dict], return_as_dict: bool\n    ) -> Any:\n        if params is None:\n            params = {}\n        callback = self._connection._send_message_to_server(\n            self._object, method, params\n        )\n        if self._connection._error:\n            error = self._connection._error\n            self._connection._error = None\n            raise error\n        done, _ = await asyncio.wait(\n            {\n                self._connection._transport.on_error_future,\n                callback.future,\n            },\n            return_when=asyncio.FIRST_COMPLETED,\n        )\n        if not callback.future.done():\n            callback.future.cancel()\n>       result = next(iter(done)).result()\nE       playwright._impl._errors.TimeoutError: Timeout 30000ms exceeded.\n\nD:\\qa-ui-dmweb\\vevn-dmweb\\Lib\\site-packages\\playwright\\_impl\\_connection.py:100: TimeoutError"}, "description": "\n        【102480】 WEB-注册-onboarding-页面UI/UX验证\n        ", "start": 1747361069309, "stop": 1747361255417, "uuid": "a17b8ef2-65b2-4c1a-9b4c-0d696a5804c9", "historyId": "d11ee7cf860ea00cd48878b003a20a14", "testCaseId": "d11ee7cf860ea00cd48878b003a20a14", "fullName": "src.Dweb.EC.testcases.dweb_home.test_102480_web_signup_onboarding_ui_ux.TestWebSignupOnboardingUIUX#test_102480_web_signup_onboarding_ui_ux", "labels": [{"name": "story", "value": "WEB-注册-onboarding-页面UI/UX验证"}, {"name": "tag", "value": "case"}, {"name": "tag", "value": "test"}, {"name": "tag", "value": "dweb_regression"}, {"name": "parentSuite", "value": "src.Dweb.EC.testcases.dweb_home"}, {"name": "suite", "value": "test_102480_web_signup_onboarding_ui_ux"}, {"name": "subSuite", "value": "TestWebSignupOnboardingUIUX"}, {"name": "host", "value": "SHLAP10427"}, {"name": "thread", "value": "30276-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "src.Dweb.EC.testcases.dweb_home.test_102480_web_signup_onboarding_ui_ux"}]}