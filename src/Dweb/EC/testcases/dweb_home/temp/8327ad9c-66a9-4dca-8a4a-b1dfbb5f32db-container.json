{"uuid": "94f643ae-cff9-4998-92fa-ba4f79977d0b", "children": ["89f31943-9ed4-4f0b-9981-bde5425ed82a"], "befores": [{"name": "page", "status": "passed", "start": 1746687537912, "stop": 1746687539832}], "afters": [{"name": "page::0", "status": "broken", "statusDetails": {"message": "playwright._impl._errors.TargetClosedError: Target page, context or browser has been closed\n", "trace": "  File \"D:\\qa-ui-dmweb\\vevn-dmweb\\Lib\\site-packages\\allure_commons\\_allure.py\", line 221, in __call__\n    return self._fixture_function(*args, **kwargs)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"D:\\qa-ui-dmweb\\vevn-dmweb\\Lib\\site-packages\\_pytest\\fixtures.py\", line 911, in _teardown_yield_fixture\n    next(it)\n  File \"D:\\projects\\qa-ui-dmweb\\src\\Dweb\\EC\\conftest.py\", line 200, in page\n    context.close()\n  File \"D:\\qa-ui-dmweb\\vevn-dmweb\\Lib\\site-packages\\playwright\\sync_api\\_generated.py\", line 13818, in close\n    return mapping.from_maybe_impl(self._sync(self._impl_obj.close(reason=reason)))\n                                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"D:\\qa-ui-dmweb\\vevn-dmweb\\Lib\\site-packages\\playwright\\_impl\\_sync_base.py\", line 115, in _sync\n    return task.result()\n           ^^^^^^^^^^^^^\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\asyncio\\futures.py\", line 203, in result\n    raise self._exception.with_traceback(self._exception_tb)\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\asyncio\\tasks.py\", line 267, in __step\n    result = coro.send(None)\n             ^^^^^^^^^^^^^^^\n  File \"D:\\qa-ui-dmweb\\vevn-dmweb\\Lib\\site-packages\\playwright\\_impl\\_browser_context.py\", line 485, in close\n    await self._channel.send(\"close\", filter_none({\"reason\": reason}))\n  File \"D:\\qa-ui-dmweb\\vevn-dmweb\\Lib\\site-packages\\playwright\\_impl\\_connection.py\", line 62, in send\n    return await self._connection.wrap_api_call(\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"D:\\qa-ui-dmweb\\vevn-dmweb\\Lib\\site-packages\\playwright\\_impl\\_connection.py\", line 492, in wrap_api_call\n    return await cb()\n           ^^^^^^^^^^\n  File \"D:\\qa-ui-dmweb\\vevn-dmweb\\Lib\\site-packages\\playwright\\_impl\\_connection.py\", line 100, in inner_send\n    result = next(iter(done)).result()\n             ^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\asyncio\\futures.py\", line 203, in result\n    raise self._exception.with_traceback(self._exception_tb)\n"}, "start": 1746687634844, "stop": 1746687634972}], "start": 1746687537912, "stop": 1746687634972}