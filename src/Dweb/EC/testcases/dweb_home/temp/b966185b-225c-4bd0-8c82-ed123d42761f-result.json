{"name": "explore store的页面验证", "status": "passed", "description": "\n        MS用例号: 110756: 切换zipcode逻辑\n        ", "start": 1744073999165, "stop": 1744074026427, "uuid": "73346f72-9349-4383-a55f-048549f9b359", "historyId": "02f07f94ce5c9f7eeb43a1dd24832f75", "testCaseId": "02f07f94ce5c9f7eeb43a1dd24832f75", "fullName": "src.Dweb.EC.testcases.dweb_home.test_003_page_home.TestAtHome#test_each_store_page_check", "labels": [{"name": "story", "value": "首页搜索by category"}, {"name": "tag", "value": "case"}, {"name": "tag", "value": "test"}, {"name": "tag", "value": "dweb_regression"}, {"name": "parentSuite", "value": "src.Dweb.EC.testcases.dweb_home"}, {"name": "suite", "value": "test_003_page_home"}, {"name": "subSuite", "value": "TestAtHome"}, {"name": "host", "value": "SHLAP10427"}, {"name": "thread", "value": "7352-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "src.Dweb.EC.testcases.dweb_home.test_003_page_home"}]}