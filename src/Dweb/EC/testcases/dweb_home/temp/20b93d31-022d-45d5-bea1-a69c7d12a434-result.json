{"name": "首页按任意关键字搜索，关键字为tofu", "status": "passed", "parameters": [{"name": "data", "value": "'tofu'"}], "start": 1747373974730, "stop": 1747374006914, "uuid": "e9f1dccb-eeb3-421e-b7eb-ac6f5ba6f2ce", "historyId": "11355433699e4c9da61042a346f75537", "testCaseId": "d4087ce8b18cc14c6e866655283f4378", "fullName": "src.Dweb.EC.testcases.dweb_home.test_002_home_search_page.TestSearchAtHome#test_search_by_category", "labels": [{"name": "story", "value": "首页搜索"}, {"name": "tag", "value": "case"}, {"name": "tag", "value": "test"}, {"name": "tag", "value": "dweb_regression"}, {"name": "parentSuite", "value": "src.Dweb.EC.testcases.dweb_home"}, {"name": "suite", "value": "test_002_home_search_page"}, {"name": "subSuite", "value": "TestSearchAtHome"}, {"name": "host", "value": "SHLAP10427"}, {"name": "thread", "value": "17308-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "src.Dweb.EC.testcases.dweb_home.test_002_home_search_page"}]}