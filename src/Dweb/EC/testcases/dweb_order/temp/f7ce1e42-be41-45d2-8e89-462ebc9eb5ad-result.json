{"name": "PC端-订单页面状态标签切换UI/UX验证", "status": "passed", "description": "\n        case_id: 111250 查看我的订单\n        PC端-订单页面状态标签切换UI/UX验证\n        此用例的校验点有：\n        1. 进入account页面\n        2. 点击my orders，进入order页面\n        3. 依次点击\"pending, unshipped, shipped, to review, cancelled\"标签，查看订单\n        4. 验证每个标签页面正确加载并显示相应状态的订单\n        ", "start": *************, "stop": *************, "uuid": "14200da9-d49b-48b3-adc1-fbb449c45ae9", "historyId": "9f85c03db51a9190887cad32e395acd3", "testCaseId": "9f85c03db51a9190887cad32e395acd3", "fullName": "src.Dweb.EC.testcases.dweb_order.test_111250_dWeb_orders_ui_ux.TestDWebOrdersUIUX#test_111250_dWeb_orders_status_tabs_ui_ux", "labels": [{"name": "story", "value": "PC端-订单页面UI/UX验证"}, {"name": "tag", "value": "case"}, {"name": "tag", "value": "test"}, {"name": "tag", "value": "pcorders"}, {"name": "tag", "value": "regression"}, {"name": "parentSuite", "value": "src.Dweb.EC.testcases.dweb_order"}, {"name": "suite", "value": "test_111250_dWeb_orders_ui_ux"}, {"name": "subSuite", "value": "TestDWebOrdersUIUX"}, {"name": "host", "value": "SHLAP10427"}, {"name": "thread", "value": "34832-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "src.Dweb.EC.testcases.dweb_order.test_111250_dWeb_orders_ui_ux"}]}