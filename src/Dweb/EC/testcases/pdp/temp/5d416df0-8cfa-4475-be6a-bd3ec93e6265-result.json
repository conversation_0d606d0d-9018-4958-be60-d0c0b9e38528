{"name": "进入fbw pdp页面检查元素并加购", "status": "passed", "description": "\n        100616 验证Global+ FBW 商品PDP UI-UX\n        ", "parameters": [{"name": "pdp_fbw_url", "value": "'https://www.sayweee.com/en/product/Pork-floss-with-mochi-cake-2pc/2044455?category=freshbakery02&parent_category=freshbakery'"}], "start": 1738981536249, "stop": 1738981562425, "uuid": "cead92f6-80d8-4b2f-a706-62fafdd4d274", "historyId": "58877166a096e78a9cf15a40e5145c39", "testCaseId": "cca949d505af92d8380509c1a979d4e0", "fullName": "src.Dweb.EC.testcases.pdp.test_100_pdp_page_check.TestAtPDP#test_check_fbw_pdp_and_add_to_cart", "labels": [{"name": "story", "value": "产品PDP页面校验"}, {"name": "tag", "value": "case"}, {"name": "tag", "value": "test"}, {"name": "tag", "value": "pdp"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "src.Dweb.EC.testcases.pdp"}, {"name": "suite", "value": "test_100_pdp_page_check"}, {"name": "subSuite", "value": "TestAtPDP"}, {"name": "host", "value": "SHLAP10427"}, {"name": "thread", "value": "23268-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "src.Dweb.EC.testcases.pdp.test_100_pdp_page_check"}]}