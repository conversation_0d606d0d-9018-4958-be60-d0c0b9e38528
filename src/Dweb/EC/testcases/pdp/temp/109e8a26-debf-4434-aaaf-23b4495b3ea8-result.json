{"name": "进入pdp页面检查元素并加购", "status": "passed", "description": "\n        training\n        ", "parameters": [{"name": "pdp_url", "value": "'https://www.sayweee.com/en/product/Calbee-Takoyaki-Ball/18362?category=snack02&parent_category=snack'"}], "start": 1738921973172, "stop": 1738921994506, "uuid": "9c74ca96-75fa-4808-be08-36de327db41c", "historyId": "a1f3a4243bb072d9f15bf0f7e87b0919", "testCaseId": "f57621e3f6db3f25ac5e1f991578253c", "fullName": "src.Dweb.EC.testcases.pdp.test_100_pdp_page_check.TestAtPDP#test_check_pdp_and_add_to_cart", "labels": [{"name": "story", "value": "产品PDP页面校验"}, {"name": "tag", "value": "case"}, {"name": "tag", "value": "test"}, {"name": "tag", "value": "pdp"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "src.Dweb.EC.testcases.pdp"}, {"name": "suite", "value": "test_100_pdp_page_check"}, {"name": "subSuite", "value": "TestAtPDP"}, {"name": "host", "value": "SHLAP10427"}, {"name": "thread", "value": "8612-Main<PERSON>hread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "src.Dweb.EC.testcases.pdp.test_100_pdp_page_check"}]}