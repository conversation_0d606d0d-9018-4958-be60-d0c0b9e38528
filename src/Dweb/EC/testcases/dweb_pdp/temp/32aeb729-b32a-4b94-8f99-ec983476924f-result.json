{"name": "【112065】 PC-PDP-商品组-只有一个属性模块UI/UX验证", "status": "broken", "statusDetails": {"message": "AttributeError: 'NoneType' object has no attribute 'is_visible'", "trace": "self = <src.Dweb.EC.testcases.dweb_pdp.test_112065_dWeb_pdp_product_group_ui_ux.TestDWebPDPProductGroupUIUX object at 0x00000192F5EBB710>\npage = {'context': <BrowserContext browser=<Browser type=<BrowserType name=chromium executable_path=C:\\Users\\<USER>\\App...n=120.0.6099.28>>, 'page': <Page url='https://www.sayweee.com/en/product/Vita-Honey-Chrysanthemum-Tea-250ml-6/106422'>}\npc_autotest_header = {'Content-Type': 'application/json;charset=UTF-8', 'Zipcode': '98011', 'app-version': 'null', 'authorization': 'Bearer...EFgXz31afreKhN6FTcjgRZQIS4aHo9RCH5kvBl2py-dF6TgtW8Mm6rFU0QwKK6GD51Xgdu4lqfDlpyAMbueQT4fddlj-d2E5hRc_Vm4d_2HGA0xs', ...}\nlogin_trace = None\n\n    @allure.title(\"【112065】 PC-PDP-商品组-只有一个属性模块UI/UX验证\")\n    def test_112065_dWeb_pdp_product_group_ui_ux(self, page: dict, pc_autotest_header, login_trace):\n        \"\"\"\n        【112065】 PC-PDP-商品组-只有一个属性模块UI/UX验证\n        \"\"\"\n        p: Page = page.get(\"page\")\n        c = page.get(\"context\")\n        # 1.直接进入指定pdp页面\n        pdp_page = PDPPage(p, pc_autotest_header, browser_context=c,\n                           page_url=\"/product/Vita-Honey-Chrysanthemum-Tea-250ml-6/106422\")\n        initial_url = p.url\n        # 2.滚动到指定位置\n        scroll_one_page_until(p, dweb_pdp_ele.ele_product_group)\n        # 3.断言product group 文案存在\n>       assert pdp_page.FE.ele(dweb_pdp_ele.ele_product_group_title).is_visible()\nE       AttributeError: 'NoneType' object has no attribute 'is_visible'\n\ntest_112065_dWeb_pdp_product_group_ui_ux.py:28: AttributeError"}, "description": "\n        【112065】 PC-PDP-商品组-只有一个属性模块UI/UX验证\n        ", "start": 1744012909624, "stop": 1744012969345, "uuid": "61b8aee9-1120-4780-8c92-25a0fab96fab", "historyId": "2017de3c5ed4f047cdd43d04b5d65b11", "testCaseId": "2017de3c5ed4f047cdd43d04b5d65b11", "fullName": "src.Dweb.EC.testcases.dweb_pdp.test_112065_dWeb_pdp_product_group_ui_ux.TestDWebPDPProductGroupUIUX#test_112065_dWeb_pdp_product_group_ui_ux", "labels": [{"name": "story", "value": "【112065】 PC-PDP-商品组-只有一个属性模块UI/UX验证"}, {"name": "tag", "value": "case"}, {"name": "tag", "value": "test"}, {"name": "tag", "value": "pcpdp"}, {"name": "tag", "value": "dweb_regression"}, {"name": "tag", "value": "transaction"}, {"name": "tag", "value": "suqin"}, {"name": "parentSuite", "value": "src.Dweb.EC.testcases.dweb_pdp"}, {"name": "suite", "value": "test_112065_dWeb_pdp_product_group_ui_ux"}, {"name": "subSuite", "value": "TestDWebPDPProductGroupUIUX"}, {"name": "host", "value": "SHLAP10427"}, {"name": "thread", "value": "26844-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "src.Dweb.EC.testcases.dweb_pdp.test_112065_dWeb_pdp_product_group_ui_ux"}]}