{"name": "PC端-从结算页面新增地址UI/UX验证", "status": "passed", "description": "\n        PC端-从结算页面新增地址UI/UX验证\n        此用例的校验点有：\n        1. 进入结算页面，点击地址选择区域（使用get_by_test_id定位）\n        2. 点击\"新增地址\"按钮（使用get_by_test_id定位），进入新增地址页面\n        3. 填写地址信息（街道地址、姓名、电话、备注）\n        4. 保存地址，验证地址簿中新增了一条地址\n        ", "start": 1745309701007, "stop": 1745309747210, "uuid": "af9f777b-1528-4fee-9664-315972f356b4", "historyId": "6f1171dcdc8af10306af5582c9a7ccee", "testCaseId": "6f1171dcdc8af10306af5582c9a7ccee", "fullName": "src.Dweb.EC.testcases.dweb_address.test_110841_dWeb_add_address_ui_ux.TestDWebAddAddressUIUX#test_110841_dWeb_add_address_from_checkout_ui_ux", "labels": [{"name": "story", "value": "PC端-新增地址UI/UX验证"}, {"name": "tag", "value": "case"}, {"name": "tag", "value": "test"}, {"name": "tag", "value": "dweb_regression"}, {"name": "parentSuite", "value": "src.Dweb.EC.testcases.dweb_address"}, {"name": "suite", "value": "test_110841_dWeb_add_address_ui_ux"}, {"name": "subSuite", "value": "TestDWebAddAddressUIUX"}, {"name": "host", "value": "SHLAP10427"}, {"name": "thread", "value": "3976-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "src.Dweb.EC.testcases.dweb_address.test_110841_dWeb_add_address_ui_ux"}]}