{"name": "PC端-从结算页面新增地址UI/UX验证", "status": "passed", "description": "\n        PC端-从结算页面新增地址UI/UX验证\n        此用例的校验点有：\n        1. 进入结算页面，点击地址选择区域（使用get_by_test_id定位）\n        2. 点击\"新增地址\"按钮（使用get_by_test_id定位），进入新增地址页面\n        3. 填写地址信息（街道地址、姓名、电话、备注）\n        4. 保存地址，验证地址簿中新增了一条地址\n        ", "start": 1747376415294, "stop": 1747376656251, "uuid": "248e090f-c375-48f3-a1d9-61fb2d5cf2e5", "testCaseId": "6f1171dcdc8af10306af5582c9a7ccee", "fullName": "src.Dweb.EC.testcases.dweb_address.test_110841_dWeb_add_address_ui_ux.TestDWebAddAddressUIUX#test_110841_dWeb_add_address_from_checkout_ui_ux"}