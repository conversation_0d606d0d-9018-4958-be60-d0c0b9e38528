{"name": "PC端-多页面新增地址综合UI/UX验证", "status": "skipped", "statusDetails": {"message": "Skipped: not implemented yet", "trace": "('D:\\\\projects\\\\qa-ui-dmweb\\\\src\\\\Dweb\\\\EC\\\\testcases\\\\dweb_address\\\\test_110841_dWeb_add_address_ui_ux.py', 94, 'Skipped: not implemented yet')"}, "description": "\n        PC端-多页面新增地址综合UI/UX验证\n        此用例综合验证从不同页面新增地址的功能：\n        1. 从首页新增地址（所有元素定位都使用get_by_test_id方法）\n        2. 从账户页面新增地址（所有元素定位都使用get_by_test_id方法）\n        3. 从结算页面新增地址（所有元素定位都使用get_by_test_id方法）\n        每次新增地址后都会验证地址簿中是否新增了地址，并在测试后删除测试地址\n        ", "start": 1747295057681, "stop": 1747295057681, "uuid": "b647aa02-3eee-43ff-a731-d6551e0f9225", "historyId": "8f4adde10f538bac86ace75f27b580f2", "testCaseId": "8f4adde10f538bac86ace75f27b580f2", "fullName": "src.Dweb.EC.testcases.dweb_address.test_110841_dWeb_add_address_ui_ux.TestDWebAddAddressUIUX#test_dWeb_add_address_comprehensive_ui_ux", "labels": [{"name": "story", "value": "PC端-新增地址UI/UX验证"}, {"name": "tag", "value": "@pytest.mark.skip(reason='not implemented yet')"}, {"name": "tag", "value": "case"}, {"name": "tag", "value": "test"}, {"name": "tag", "value": "dweb_regression"}, {"name": "parentSuite", "value": "src.Dweb.EC.testcases.dweb_address"}, {"name": "suite", "value": "test_110841_dWeb_add_address_ui_ux"}, {"name": "subSuite", "value": "TestDWebAddAddressUIUX"}, {"name": "host", "value": "SHLAP10427"}, {"name": "thread", "value": "21288-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "src.Dweb.EC.testcases.dweb_address.test_110841_dWeb_add_address_ui_ux"}]}