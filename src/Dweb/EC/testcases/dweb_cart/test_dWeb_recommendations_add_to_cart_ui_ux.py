import allure
import pytest

from src.Dweb.EC.dweb_ele.dweb_cart import dweb_cart_ele
from src.Dweb.EC.dweb_pages.dweb_page_cart.dweb_page_cart import DWebCartPage
from src.common.commonui import scroll_one_page_until
from playwright.sync_api import Page
from src.config.weee.log_help import log
from src.common.commfunc import empty_cart


@allure.story("PC购物车-推荐商品加购UI/UX验证")
class TestDWebRecommendationsAddToCartUIUX:

    @allure.title("PC购物车-推荐商品加购UI/UX验证")
    def test_dWeb_recommendations_add_to_cart_ui_ux(self, page: dict, pc_autotest_header, login_trace):
        """
        PC购物车-推荐商品加购UI/UX验证
        此用例的校验点有：
        1. 进入购物车，清空购物车
        2. 滚动到推荐商品区域
        3. 验证推荐商品区域的UI元素
        4. 从推荐商品区域添加商品到购物车
        5. 验证购物车中已添加推荐商品
        6. 验证购物车小计金额更新
        """
        p: Page = page.get("page")
        c = page.get("context")
        cart_page = DWebCartPage(p, pc_autotest_header, browser_context=c, page_url="/cart")

        # 清空购物车
        try:
            empty_cart(pc_autotest_header)
        except Exception as e:
            log.info("清空购物车发生异常" + str(e))
        # 刷新页面
        p.reload()
        p.wait_for_timeout(3000)

        # 断言空购物车img存在
        assert p.get_by_test_id("wid-empty-cart-image").is_visible()
        # 断言空购物车文案存在
        assert p.get_by_test_id("wid-empty-cart-text").is_visible()

        # 滚动到推荐商品区域
        scroll_one_page_until(p, "[data-testid='wid-recommendations-section']")
        p.wait_for_timeout(2000)

        # 验证推荐商品区域存在
        assert p.get_by_test_id("wid-recommendations-section").is_visible()
        
        # 获取推荐商品
        recommend_cards = p.get_by_test_id("wid-recommendation-item").all()
        assert len(recommend_cards) > 0, "推荐商品区域没有商品"
        
        # 记录推荐商品的名称
        first_product_name = None
        for index, card in enumerate(recommend_cards):
            if index == 0:
                product_name_element = card.get_by_test_id("wid-product-name")
                if product_name_element.is_visible():
                    first_product_name = product_name_element.text_content()
                    log.info(f"将要加购的推荐商品名称: {first_product_name}")
                break
        
        assert first_product_name is not None, "无法获取推荐商品名称"
        
        # 点击第一个推荐商品的加购按钮
        add_to_cart_button = recommend_cards[0].get_by_test_id("btn-atc-plus")
        assert add_to_cart_button.is_visible(), "加购按钮不可见"
        add_to_cart_button.click()
        p.wait_for_timeout(3000)
        
        # 验证购物车图标上的数量更新
        cart_count = p.get_by_test_id("wid-mini-cart").locator("//span[contains(@class,'MiniCart_cartIconCountQty')]")
        assert cart_count.is_visible(), "购物车数量不可见"
        assert cart_count.text_content() != "0", "购物车数量未更新"
        
        # 滚动回购物车顶部
        p.get_by_test_id("wid-mini-cart").scroll_into_view_if_needed()
        p.wait_for_timeout(2000)
        
        # 点击购物车图标查看购物车
        p.get_by_test_id("wid-mini-cart").click()
        p.wait_for_timeout(3000)
        
        # 验证购物车中有商品
        assert p.get_by_test_id("wid-cart-section-goods").is_visible()
        
        # 验证购物车中包含刚才加购的推荐商品
        cart_product_titles = p.get_by_test_id("wid-cart-item-name").all()
        found_product = False
        for title in cart_product_titles:
            if first_product_name in title.text_content():
                found_product = True
                break
        
        assert found_product, f"购物车中未找到刚才加购的推荐商品: {first_product_name}"
        
        # 验证购物车小计金额更新
        subtotal = p.get_by_test_id("wid-cart-summary-subtotal").locator("//span[2]")
        assert subtotal.is_visible(), "购物车小计不可见"
        subtotal_text = subtotal.text_content()
        assert "$" in subtotal_text, f"购物车小计金额格式不正确: {subtotal_text}"
        log.info(f"购物车小计金额: {subtotal_text}")
