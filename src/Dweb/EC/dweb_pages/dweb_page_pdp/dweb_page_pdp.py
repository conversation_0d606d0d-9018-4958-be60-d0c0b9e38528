import datetime
from src.config.base_config import TEST_URL
from playwright.sync_api import Page
from src.Dweb.EC.dweb_ele.dweb_product.dweb_pdp_ele import *
from src.api.porder import update_pickup_date
from src.common.commfunc import empty_cart

from src.config.weee.log_help import log
from src.Dweb.EC.dweb_pages.dweb_common_page import DWebCommonPage


class DWebPDPPage(DWebCommonPage):
    """
    这个类主要封装了PC web端对pdp页面的封装, pdp页面表示的是商品详情页面
    """
    def __init__(self, page: Page, header, browser_context, page_url: str = ''):
        """
        构造方法主要包含以下功能：
        1. 进入pdp页面
        2. 清空购物车
        3. 更新送货日期，否则fbw容易没货
        """
        super().__init__(page, header)
        self.bc = browser_context
        # 直接进入PDP页面
        self.page.goto(TEST_URL + page_url)
        # 清空购物车
        try:
            empty_cart(self.header)
        except Exception as e:
            log.info("清空购物车发生异常" + str(e))

        # 更新送货日期，否则fbw容易没货
        update_pickup_date(self.header, (datetime.datetime.now() + datetime.timedelta(days=2)).strftime('%Y-%m-%d'))

    def goto_pdp_and_check(self, pdp_url):
        """
        此方法包含以下功能：
        1. 进入pdp页面
        2. 校验页面， 包括商品的title, price, thumbnail, primary image
        3. 将related商品加入购物车
        """
        # 1. 进入pdp页面
        self.page.goto(pdp_url)

        # 2. 校验页面
        self._check_pdp_page()

        # 3. 将related商品加入购物车
        related_add_to_cart_button_list = self.FE.eles(ele_pdp_related_add_to_cart_button)
        self.add_pdp_related_products_to_cart(related_add_to_cart_button_list, 3)

    def goto_fbw_pdp_and_check(self, url):
        """
        此方法包含以下功能：
        1. 进入fbw pdp页面, fbw指的是fresh bakery的商品
        2. 校验页面， 包括商品的title, price, thumbnail, primary image
        3. 将related商品加入购物车
        """
        # 1. 进入fbw pdp页面
        self.page.goto(url)

        # 2. 元素校验
        self._check_fbw_pdp_page()

        # 3. 将fbw mweb_page_pdp related商品加入购物车
        # 这种用locator的方式有问题，会导致页面刷新，按钮次序不对
        # fbw_related_products_add_to_cart_buttons = self.page.get_by_test_id("wid-mweb_page_pdp-related-card").get_by_test_id("btn-atc-plus").all()
        fbw_related_products_add_to_cart_buttons = self.page.get_by_test_id("wid-mweb_page_pdp-related-card").get_by_test_id(
            "btn-atc-plus").element_handles()
        if fbw_related_products_add_to_cart_buttons:
            self.add_pdp_related_products_to_cart(fbw_related_products_add_to_cart_buttons, 3)

    def _check_pdp_page(self):
        """
        该方法包含以下功能：
        1. 校验pdp页面的元素，包括商品的title, price, thumbnail, primary image
        2. pdp_dweb_page_common_check方法是基类提供的，在子类中调用
        """
        # networkidle比load, domcontentloaded更稳定
        self.page.wait_for_load_state("networkidle", timeout=60000)
        self.pdp_dweb_page_common_check()
        self.FE.ele(ele_pdp_header_content).is_visible()
        self.page.get_by_test_id("wid-mweb_page_pdp-related-card").is_visible()

    def _check_fbw_pdp_page(self):
        """
        该方法包含以下功能：
        1. 校验fbw pdp页面的元素，包括商品的title, price, thumbnail, primary image
        2. pdp_dweb_page_common_check方法是基类提供的，在子类中调用
        3. fbw商品与普通商品有细微区别：页面上有Freshly Made的span
        """
        self.page.wait_for_timeout(3000)
        self.pdp_dweb_page_common_check()
        assert self.FE.ele("//span[text()=' Freshly Made']").is_visible()
        assert self.FE.ele("//span[text()='Shop more']").is_visible()
        # 商品名
        assert self.page.locator("div[class^='Header_desc'] h2").is_visible()
        # 商品 subname
        assert self.page.locator("div[class^='Header_subname']").is_visible()
        # 商品价格
        assert self.page.locator(
            "div[class^='Header_desc'] div[class^='Header_price_price']").text_content().startswith("$")
        # related products
        assert self.page.get_by_test_id("wid-pdp-related-card").get_by_test_id(
            "wid-product-card-container").all(), f"fbw pdp页面没有找到related products"
