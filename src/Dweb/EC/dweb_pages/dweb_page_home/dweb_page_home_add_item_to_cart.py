import re
from typing import List

from playwright.sync_api import Page, ElementHandle

from src.Dweb.EC.dweb_ele.dweb_cart.dweb_cart_ele import *
from src.Dweb.EC.dweb_ele.dweb_home.dweb_home_ele import *
from src.common.commfunc import empty_cart
from src.common.commonui import close_popup_on_home, close_advertise_on_home
from src.config.base_config import TEST_URL
from src.config.weee.log_help import log
from src.Dweb.EC.dweb_pages.dweb_common_page import DWebCommonPage


class AddItemToCartPage(DWebCommonPage):
    """
    该类主要描述了如何将首页各种不同合集的商品加入购物车，并校验购物车
    """

    def __init__(self, page: Page, header):
        super().__init__(page, header)

    def add_to_cart(self):
        """
        该方法包含以下功能：
        1. 进入首页，缓慢滑动到页面底部，使首页所有的组件都正常加载，因为页面是动态显示，所以需要先滑动到页面底部
        2. 滑动到每一个合集，合集包括：editor's pick, new arrival, bestsellers, global+, fresh daily, everyday deals, recommended for you
        3. 每个合集的商品，点击加购按钮，加购4个商品
        4. 点击购物车，进入购物车页面，校验购物车的UI，并checkout
        5. 点击place order按钮，产生订单
        """
        self.page.goto(TEST_URL)
        self.page.wait_for_timeout(10000)
        self.page.wait_for_load_state("networkidle", timeout=60000)

        # close_popup_on_home(self.page)
        close_advertise_on_home(self.page)

        # 清空购物车
        try:
            empty_cart(self.header)
        except Exception as e:
            log.info("清空购物车发生异常" + str(e))

        self.page.reload()
        close_popup_on_home(self.page)
        close_advertise_on_home(self.page)

        while True:
            self.page.evaluate('window.scrollBy(0, window.innerHeight)')
            self.page.wait_for_timeout(2000)
            if self.FE.ele(u"//div[text()='Featured Reviews']"):
                break

        # 1. 加购editor's pick
        self.add_each_collections_product_to_cart(ele_home_editors_pick_add_to_cart, index=1, c_selector=u"""//div[text()="Editor's Pick"]""")

        # 2. 加购new arrival
        self.add_each_collections_product_to_cart(ele_home_new_arrival_add_to_cart, index=2, c_selector=u"//div[text()='New Arrivals']")

        # 3. 加购 Bestsellers
        self.add_each_collections_product_to_cart(ele_home_bestsellers_add_to_cart, index=3, c_selector=u"//div[text()='Bestsellers']")

        # 4. 加购Global+
        self.add_each_collections_product_to_cart(ele_home_global_plus_add_to_cart, index=4, c_selector=u"//div[contains(text(),'Global+ Top')]")

        # 5. fresh daily
        self.add_each_collections_product_to_cart(ele_home_fresh_daily_add_to_cart, index=5, c_selector=u"//div[text()='Fresh Daily']")

        # 6. everyday deals
        self.add_each_collections_product_to_cart(ele_home_everyday_deals_add_to_cart, index=6, c_selector=u"//div[text()='Everyday deals']")

        # 7. recommended for you
        self.add_each_collections_product_to_cart(ele_home_recommended_for_you_add_to_cart, index=7, c_selector=u"//div[text()='Recommended For You']")

        # 8. 去购物车结算
        self.go_to_cart_from_home()


    def add_each_collections_product_to_cart(self, collections, index, c_selector):
        """
        该方法包含以下功能：
        1. 根据传入的collections, 滑动到合集，点击加购按钮，加购4个商品
        """
        self.page.wait_for_timeout(2000)
        # 必须用query_selector来scroll_into_view,page.locator就无效
        self.FE.ele(c_selector).scroll_into_view_if_needed()
        # self.page.locator(c_selector).hover()
        # 为什么locator.scroll_into_view_if_need就无效？
        # self.page.locator(c_selector).scroll_into_view_if_needed(timeout=2000)
        each_collection = self.page.query_selector_all(collections)
        assert each_collection, f"首页没有可以加购的商品，each_collection={collections}"
        log.info("each_collection===>" + str(each_collection))
        for index, item in enumerate(each_collection):
            try:
                item.evaluate('(item) => item.click()')
                # item.click()
            except Exception as e:
                log.info("加购按钮点击失败")
            # self.page.reload()
            self.page.wait_for_timeout(1000)
            if index == 3:
                break

    def go_to_cart_from_home(self):
        """
        该方法包含以下功能：
        1. 从首页进入购物车页面，校验购物车的UI样式，并checkout
        """
        self.FE.ele(ele_home_cart).click()
        self.page.wait_for_load_state("networkidle", timeout=60000)
        # 校验购物车样式-{【109546】 PC购物车-多个购物车样式}
        self.check_cart_style_ui()
        # 点击checkout
        self.FE.ele(ele_cart_select_carts_to_checkout).click()
        self.page.wait_for_timeout(3000)
        self.FE.ele(ele_cart_select_all_carts).click()
        self.page.wait_for_timeout(3000)
        self.FE.ele(ele_cart_checkout).click()
        self.page.wait_for_load_state("load", timeout=60000)
        # 如果有upsell,处理upsell
        if self.FE.ele(ele_cart_upsell_continue_checkout):
            self.FE.ele(ele_cart_upsell_continue_checkout).click()
        # 如果没有选择地址， 则选择地址
        if self.FE.ele(ele_cart_select_address):
            self.FE.ele(ele_cart_select_address).click()
        # place order
        # self.FE.ele(ele_cart_place_order).click()

    def check_cart_style_ui(self):
        """
        # 校验购物车样式-{【109546】 PC购物车-多个购物车样式}
        :return:
        """
        self.page.wait_for_selector(ele_cart_summary)
        assert self.FE.ele(ele_cart_summary).is_visible()
        assert "title" in self.FE.ele(ele_cart_summary).get_attribute("class")
        # 0. 判断第一个购物车是local delivery
        assert "Local delivery" == self.FE.ele(ele_cart_summary_local_delivery).text_content()
        # 1. 判断subtotal元素存在
        assert self.FE.ele(ele_cart_subtotal).is_visible()
        sub_total_fee = self.FE.ele(ele_cart_subtotal_fee)
        # 2. 判断subtotal值
        assert sub_total_fee.is_visible() and "$" in sub_total_fee.text_content()

        # 获取所有的items total
        items_total = self.FE.eles(ele_cart_items_total)
        assert items_total, f"items_total={items_total}"
        # 3. 判断items_total中有美元符号存在
        for item in items_total:
            log.debug("item.text_content===>" + item.text_content())
            assert "$" in item.text_content()

        # 4. 判断delivery_fee中有美元符号存在或为free
        delivery_fee = self.FE.eles(ele_cart_delivery_fee)
        for df in delivery_fee:
            log.debug("delivery_fee的content===>" + df.text_content())
            assert "$" in df.text_content() or 'Free' == df.text_content()

        # 5. 判断左侧的购物车
        all_cart_div = self.FE.eles(ele_cart_each_cart_div)
        assert all_cart_div, f"all_cart_div={all_cart_div}"
        for acd in all_cart_div:
            all_goods: List[ElementHandle] = acd.query_selector_all("//div[contains(@class, 'GoodsInCart_goods__')]")
            assert all_goods, f"all_goods={all_goods}"
            for index, ag in enumerate(all_goods):
                goods_in_cart_price_action = ag.query_selector(u"//div[contains(@class, 'GoodsInCart_goodsActions')]//div[contains(@class, 'GoodsInCart_priceAction')]")
                # 校验购物车里"每个商品"的div
                assert goods_in_cart_price_action.is_visible()
                goods_in_cart_price = ag.query_selector(u"//div[contains(@class, 'GoodsInCart_goodsActions')]//div[contains(@class, 'leading-none font-semibold text-center')]")
                log.info("each product price in cart===>" + goods_in_cart_price.text_content())
                # 校验商品的价格以$开头
                assert "$" in goods_in_cart_price.text_content() or "Free" in goods_in_cart_price.text_content()
                # 第一个商品有可能是gift商品，没有remove和save_for_later
                if index >=2:
                    # 校验remove按钮
                    remove = ag.query_selector(u"//div[text()='Remove']")
                    # 校验save_for_later
                    save_for_later = ag.query_selector(u"//div[text()='Save for later']")
                    assert remove.is_visible() and save_for_later.is_visible()
                product_name = ag.query_selector(u"//div[contains(@class, 'GoodsInCart_name')]//span").text_content()
                log.info("product_name is: " + product_name)
                assert len(product_name) > 2

        # 7. check底部的recommendations
        # 先滚动到Recommendations
        while True:
            self.page.evaluate('window.scrollBy(0, window.innerHeight)')
            self.page.wait_for_timeout(2000)
            if self.FE.ele(u"//span[text()='Recommendations']"):
                self.FE.ele(u"//span[text()='Recommendations']").scroll_into_view_if_needed()
                break

        # 7.1 校验标题
        assert self.FE.ele(ele_cart_recommendations).text_content() == 'Recommendations'
        recommendations_all_goods = self.FE.eles(ele_cart_recommendations_all_goods)
        assert recommendations_all_goods, f"购物车推荐商品为0"
        # 7.2 校验recommendations下面的商品
        for index, i in enumerate(recommendations_all_goods):
            # 后面隐藏的商品，继续找加购按钮，找不到，可能因为不可见，需要划动
            if index <=2:
                r_add_to_cart_btn = i.query_selector(u"//i[@data-role]")
                assert r_add_to_cart_btn.is_enabled()
                r_add_to_cart_btn.click()




    def scroll_to_bottom(self):
        """
        仅用于测试，不建议AI使用
        """
        # 仅用于测试
        self.page.goto(TEST_URL)
        self.page.wait_for_timeout(5000)
        self.page.wait_for_load_state("networkidle", timeout=60000)

        close_popup_on_home(self.page)
        close_advertise_on_home(self.page)

        while True:
            # self.page.evaluate('window.scrollTo(0, document.body.scrollHeight/10)')
            self.page.evaluate('window.scrollBy(0, window.innerHeight)')
            self.page.wait_for_timeout(2000)
            if self.FE.ele(u"//div[text()='Featured Reviews']"):
                break

        print(self.FE.ele(u"""//div[text()="Editor's Pick"]"""))
        print(self.FE.ele(u"//div[text()='New Arrivals']"))
        print(self.FE.ele(u"//div[text()='Bestsellers']"))
        print(self.FE.ele(u"//div[contains(text(),'Global+ Top')]"))
        print(self.FE.ele(u"//div[text()='Fresh Daily']"))
        print(self.FE.ele(u"//div[text()='Everyday deals']"))
        print(self.FE.ele(u"//div[text()='Recommended For You']"))

        self.page.get_by_test_id(re.compile("^hello$", re.IGNORECASE))


