from playwright.sync_api import Page

from src.common.commfunc import empty_cart
from src.common.commonui import close_popup_on_home, close_advertise_on_home, ele, eles, home_init
from src.config.base_config import TEST_URL
from src.config.weee.log_help import log


class TrackHomePageOperations:
    """ 埋点首页操作，不推荐AI使用 """
    # 搜索
    ele_home_search_input = u"span[class^='Search_holderTextColor'] span:first-child"
    # ele_home_search_button = u"div[class^='Search_searchHolder'] button"
    ele_home_search_button = u"span img[title='搜索']"
    ele_search_tofu = u"//div[@data-role='addButton']"

    # zipcode
    ele_home_zipcode = u"#changeZipCode"  # id选择器
    ele_input_zipcode = u"input[class^='MaterialDesignInput_field']"  # css selector
    ele_home_zipcode_ok = u"div[class^='ChangeZipCode'] button[shape='round'][class^='Button_button']"  # css selector

    # banner css selector
    ele_home_banner = u"div[data-component='CroppedImage'] div[class^='go']"

    # 新品上架
    ele_home_new_arrival_add_to_cart = u"//div[text()='新品上架']/ancestor::div[@data-module='cm_item_line']//i[@data-role='addButtonPlusIcon']"  # xpath

    # 人气热卖
    ele_home_hot_selling_add_to_cart = u"//div[text()='人气热卖']/ancestor::div[@data-module='cm_item_line']//i[@data-role='addButtonPlusIcon']"  # xpath

    # 全球购热门榜
    ele_home_global_hot_selling_add_to_cart = u"//div[text()='全球购热门榜']/ancestor::div[contains(@class,'ProductLineWithTabs_productContent')]//i[@data-role='addButtonPlusIcon']"  # xpath

    # 特价精选
    ele_home_everyday_deals_add_to_cart = u"//div[text()='特价精选']/ancestor::div[@data-module='cm_item_line']//i[@data-role='addButtonPlusIcon']"  # xpath

    # 猜你喜欢
    ele_home_recommend_for_you = u"//div[text()='猜你喜欢']/ancestor::div[@data-module='cm_item_line']//i[@data-role='addButtonPlusIcon']"  # xpath

    def __init__(self, page: Page, header):
        self.page = page
        self.header = header

    def track_home_operations(self):
        # 清空购物车
        try:
            empty_cart(self.header)
        except Exception as e:
            log.info("清空购物车发生异常" + str(e))

        self.page.goto(TEST_URL)
        self.page.wait_for_timeout(3000)

        close_popup_on_home(self.page)
        close_advertise_on_home(self.page)


        # 切换zipcode
        ele(self.page, self.ele_home_zipcode).click()
        ele(self.page, self.ele_input_zipcode).fill("98011")
        ele(self.page, self.ele_home_zipcode_ok).click()

        # 1. 点击搜索 "豆腐"
        self._search_on_home()
        # 2. 点击banner
        self._click_banner()
        # 3. 加购新品上架
        self._click_new_arrivals()
        # 4. 加购人气热卖
        self._click_hot_selling()
        # 5. 操作global全球热购榜
        self._click_global_hot_selling()
        # 6. 加购特价精选
        self._click_everyday_deals()
        # 7. 猜你喜欢
        self._click_recommend_for_you()

    def click_all_categories(self):
        self.page.reload()
        self.page.wait_for_selector(u"//a[text()='新品上架']", timeout=5000)
        ele(self.page, u"//span[text()='餐馆卤味']").click()
        self.page.wait_for_timeout(2000)
        # 有的账号没有现做面包这个category
        # try:
        #     self.page.wait_for_selector(u"//span[text()='现做面包']")
        #     ele(self.page, u"//span[text()='现做面包']").click()
        # except Exception as e:
        #     log.info("页面上没有现做面包的span" + str(e))

        self.page.wait_for_selector(u"//span[text()='酒类']", timeout=5000)
        ele(self.page, u"//span[text()='酒类']").click()
        self.page.wait_for_timeout(2000)

        self.page.wait_for_selector(u"//span[text()='水果']", timeout=5000)
        ele(self.page, u"//span[text()='水果']").click()
        self.page.wait_for_timeout(2000)

        self.page.wait_for_selector(u"//span[text()='蔬菜']", timeout=5000)
        ele(self.page, u"//span[text()='蔬菜']").click()
        self.page.wait_for_timeout(2000)

        self.page.wait_for_selector(u"//span[text()='肉类']", timeout=5000)
        ele(self.page, u"//span[text()='肉类']").click()
        self.page.wait_for_timeout(2000)

        self.page.wait_for_selector(u"//span[text()='海鲜']", timeout=5000)
        ele(self.page, u"//span[text()='海鲜']").click()
        self.page.wait_for_timeout(2000)

        self.page.wait_for_selector(u"//span[text()='冷冻食品']", timeout=5000)
        ele(self.page, u"//span[text()='冷冻食品']").click()
        self.page.wait_for_timeout(2000)

        self.page.wait_for_selector(u"//span[text()='方便食品']", timeout=5000)
        ele(self.page, u"//span[text()='方便食品']").click()
        self.page.wait_for_timeout(2000)

        self.page.wait_for_selector(u"//span[text()='烘焙']", timeout=5000)
        ele(self.page, u"//span[text()='烘焙']").click()
        self.page.wait_for_timeout(2000)

        self.page.wait_for_selector(u"//span[text()='零食']", timeout=5000)
        ele(self.page, u"//span[text()='零食']").click()
        self.page.wait_for_timeout(2000)

        self.page.wait_for_selector(u"//span[text()='饮料']", timeout=5000)
        ele(self.page, u"//span[text()='饮料']").click()
        self.page.wait_for_timeout(2000)

        self.page.wait_for_selector(u"//span[text()='乳制品＆鸡蛋']", timeout=5000)
        ele(self.page, u"//span[text()='乳制品＆鸡蛋']").click()
        self.page.wait_for_timeout(2000)

        self.page.wait_for_selector(u"//span[text()='豆腐＆素食']", timeout=5000)
        ele(self.page, u"//span[text()='豆腐＆素食']").click()
        self.page.wait_for_timeout(2000)

        self.page.wait_for_selector(u"//span[text()='调料']", timeout=5000)
        ele(self.page, u"//span[text()='调料']").click()
        self.page.wait_for_timeout(2000)

        self.page.wait_for_selector(u"//span[text()='罐头&腌制品']", timeout=5000)
        ele(self.page, u"//span[text()='罐头&腌制品']").click()
        self.page.wait_for_timeout(2000)

        self.page.wait_for_selector(u"//span[text()='干货&米面']", timeout=5000)
        ele(self.page, u"//span[text()='干货&米面']").click()
        self.page.wait_for_timeout(2000)

        self.page.wait_for_selector(u"//span[text()='个人护理']", timeout=5000)
        ele(self.page, u"//span[text()='个人护理']").click()
        self.page.wait_for_timeout(2000)

        self.page.wait_for_selector(u"//span[text()='日用百货']", timeout=5000)
        ele(self.page, u"//span[text()='日用百货']").click()
        self.page.wait_for_timeout(2000)

    def _search_on_home(self, keyword: str = "豆腐"):
        ele(self.page, self.ele_home_search_input).click()
        self.page.wait_for_selector('#searchInput', timeout=3000)
        # 弹出
        self.page.locator('#searchInput').fill(keyword)
        search_button = ele(self.page, self.ele_home_search_button)
        search_button.click()
        # 必须等待，否则操作太快埋点可能不会上报
        self.page.wait_for_timeout(5000)
        # self.page.wait_for_selector(self.ele_search_tofu, timeout=3000)
        # 必须加上等待selector, 否则页面url不更新
        try:
            self.page.wait_for_selector(u"//span[text()='全部结果']", timeout=5000)
        except Exception as e:
            log.debug(f"没有搜索到{keyword}的结果")
        all_tofu_elements = eles(self.page, self.ele_search_tofu)
        # 加购搜索的商品
        if all_tofu_elements:
            for index, i in enumerate(all_tofu_elements):
                i.click()
                if index == 2:
                    break
        else:
            log.info("没有搜索到加购按钮")

        self.page.go_back()
        self.page.wait_for_selector(u"//a[text()='新品上架']", timeout=5000)

    def _click_banner(self):
        self.page.goto(TEST_URL)
        self.page.wait_for_timeout(5000)

        close_popup_on_home(self.page)
        close_advertise_on_home(self.page)
        log.info("开始点击banner操作")
        banners = eles(self.page, self.ele_home_banner)
        # 只点击一个banner就够了
        if banners:
            for index, banner in enumerate(banners):
                banner.click()
                # 必须等待，否则操作太快埋点可能不会上报
                self.page.wait_for_timeout(5000)
                self.page.go_back()
                self.page.wait_for_selector(u"//a[text()='新品上架']", timeout=5000)
                if index == 0:
                    break
        else:
            log.info("没有banner可以点击或banner为空")

    def _click_new_arrivals(self):
        home_init(self.page)
        new_arrival_add = eles(self.page, self.ele_home_new_arrival_add_to_cart)
        if new_arrival_add:
            for index, i in enumerate(new_arrival_add):
                # 必须等待，否则操作太快埋点可能不会上报
                self.page.wait_for_timeout(5000)
                i.click()
                if index == 2:
                    break
        else:
            log.info("没有新品数据")

    def _click_hot_selling(self):
        hot_selling = eles(self.page, self.ele_home_hot_selling_add_to_cart)
        if hot_selling:
            for index, i in enumerate(hot_selling):
                # 必须等待，否则操作太快埋点可能不会上报
                self.page.wait_for_timeout(5000)
                i.click()
                if index == 2:
                    break
        else:
            log.info("没有人气热卖数据")

    def _click_global_hot_selling(self):
        # self.page.drag_and_drop(source=u"//h2[text()='人气热卖']", target=u"//h2[text()='新品上架']")
        self.page.mouse.wheel(0, 1000)
        self.page.wait_for_selector(u"//div[text()='全球购热门榜']")
        global_hot_selling = eles(self.page, self.ele_home_global_hot_selling_add_to_cart)
        if global_hot_selling:
            for index, i in enumerate(global_hot_selling):
                # 必须等待，否则操作太快埋点可能不会上报
                self.page.wait_for_timeout(5000)
                i.click()
                if index == 2:
                    break
        else:
            log.info("没有全球购热门榜数据")

    def _click_everyday_deals(self):
        self.page.mouse.wheel(0, 800)
        everyday_deals = eles(self.page, self.ele_home_everyday_deals_add_to_cart)
        if everyday_deals:
            for index, i in enumerate(everyday_deals):
                # 必须等待，否则操作太快埋点可能不会上报
                self.page.wait_for_timeout(5000)
                i.click()
                if index == 2:
                    break
        else:
            log.info("没有特价精选数据")

    def _click_recommend_for_you(self):
        self.page.mouse.wheel(0, 600)
        recommend_for_you = eles(self.page, self.ele_home_recommend_for_you)
        if recommend_for_you:
            for index, i in enumerate(recommend_for_you):
                # 必须等待，否则操作太快埋点可能不会上报
                self.page.wait_for_timeout(5000)
                i.click()
                if index == 2:
                    break
        else:
            log.info("没有猜你喜欢数据")
