from playwright.sync_api import Locator

from src.config.weee.log_help import log
from playwright.sync_api import Page

from src.Dweb.EC.dweb_ele.dweb_home import dweb_home_ele
from src.Dweb.EC.dweb_ele.dweb_category import dweb_category_ele
from src.common.commonui import scroll_one_page_until
from src.Dweb.EC.dweb_pages.dweb_common_page import DWebCommonPage


class DWebCategorypage(DWebCommonPage):
    def __init__(self, page: Page, header, browser_context, zipcode: str = "98011"):
        super().__init__(page, header)
        self.bc = browser_context
        # 进入首页
        # self.page.goto(TEST_URL)
        # # 获取顶部语言
        # if not self.page.get_by_test_id("wid-language").locator(
        #         "//span[text()='English'and contains(@class,'Header')]").all():
        #     # 切换为英文
        #     pass
        # # 获取顶部zipocde
        # page_zipcode = self.page.locator(home_elements.ele_zipcode).text_content()
        # if page_zipcode != zipcode:
        #     switch_zipcode(headers=self.header)
        #     self.page.reload()
        # self.page.wait_for_timeout(10000)
        # close_advertise_on_home(self.page)

    def go_to_special_category_from_hone(self, special_category):
        """
        该方法包含以下功能：
        1. 从首页点击specail_category传入的分类，进入分类页
        """
        # 点击PC 上方Deals分类进入分类页
        self.FE.ele(special_category).click()
        # 等待页面加载完成
        self.page.wait_for_timeout(2000)

    def category_filter_delivery_type(self, filter_delivery_type: Locator):
        """
        该方法包含以下功能：
        1. 分类页点击勾选filter_delivery_type对应的filter来筛选不同的搜索结果
        """
        filter_delivery_type.click()
        self.page.wait_for_timeout(2000)

    def category_filter_delivery_type_check(self, filter_delivery_type: Locator):
        """
        该方法包含以下功能：
        1. 分类页点击勾选filter_delivery_type对应的filter来筛选不同的搜索结果
        """
        filter_delivery_type.click()
        self.page.wait_for_timeout(2000)

    def category_filter_delivery_type_uncheck(self, filter_delivery_type):
        """
        该方法包含以下功能：
        1. 分类页点击[取消勾选]filter_delivery_type对应的filter，来筛选不同的搜索结果
        """
        filter_delivery_type.uncheck()
        self.page.wait_for_timeout(2000)

    def category_filter_product_type_check(self, filter_product_type):
        """
        该方法包含以下功能：
        1. 分类页点击勾选filter:Product type, 根据产品类型来筛选产品，获得不同的搜索结果
        """
        filter_product_type.check()
        self.page.wait_for_timeout(2000)

    def category_filter_product_type_uncheck(self, filter_product_type):
        """
        该方法包含以下功能：
        1. 分类页点击取消勾选filter:Product type, 根据产品类型来筛选产品，获得不同的搜索结果
        """
        filter_product_type.uncheck()
        self.page.wait_for_timeout(2000)

    def special_category_filter_sub_category(self, sub_category):
        """
        该方法包含以下功能：
        1. 根据传入的特殊分类页点击切换子分类
        """
        self.FE.ele(sub_category).click()
        self.page.wait_for_timeout(2000)

    def add_product_to_cart(self, product_id):
        """
        该方法包含以下功能：
        1. 根据传入的product_id, 加购指定商品
        """
        product_id.click()
        self.page.wait_for_timeout(2000)

    def go_to_cart_from_category(self):
        """
        该方法包含以下功能：
        1. 点击购物车按钮，进入购物车页面
        """
        self.page.get_by_test_id("wid-mini-cart").click()
        self.page.wait_for_load_state("networkidle", timeout=60000)

    def add_to_local_product_cart_from_category(self):
        """
        该方法包含以下功能：
        1. 分类页delivery_type为local类型的商品
        """
        # 进入deals分类页
        self.go_to_special_category_from_hone(dweb_home_ele.ele_deals)
        # 滚动到指定位置
        scroll_one_page_until(self.page, dweb_category_ele.ele_filter_reset)
        # 点击Delivery type = Local Delivery
        delivery_type_local = self.page.get_by_test_id(dweb_category_ele.local_delivery_test_id)
        self.category_filter_delivery_type(delivery_type_local)

        self.page.wait_for_timeout(2000)
        product_ids = self.page.query_selector_all("//div[@data-testid='btn-atc-plus']")
        assert product_ids, f"本地售卖没有商品"
        for index, item in enumerate(product_ids):
            try:
                self.add_product_to_cart(item)
            except Exception as e:
                log.info("按钮加购失败" + str(e))
            if index == 1:
                break

        # 滚动到指定位置
        scroll_one_page_until(self.page, dweb_category_ele.ele_filter_reset)
        # 再调一次，点击取消Delivery type = Local Delivery
        self.category_filter_delivery_type(delivery_type_local)

    def add_to_pantry_product_cart_from_category(self):
        """
        该方法包含以下功能：
        1. 在分类页加购pantry类型的商品到购物车
        """
        # 进入deals分类页
        self.go_to_special_category_from_hone(dweb_home_ele.ele_deals)
        # 滚动到指定位置
        scroll_one_page_until(self.page, dweb_category_ele.ele_filter_reset)
        # 点击Delivery type = pantry
        delivery_type_pantry = self.page.get_by_test_id(dweb_category_ele.pantry_delivery_test_id)
        # 滚动到指定位置
        scroll_one_page_until(self.page, dweb_category_ele.ele_filter_reset)
        # 点击pantry
        self.category_filter_delivery_type(delivery_type_pantry)
        # 加购pantry售卖第一个商品
        product_ids = self.page.query_selector_all("//div[@data-testid='btn-atc-plus']")
        assert product_ids, f"本地售卖没有商品"
        for index, item in enumerate(product_ids):
            try:
                self.add_product_to_cart(item)
            except Exception as e:
                log.info("按钮加购失败" + str(e))
            if index == 1:
                break
        # 滚动到指定位置
        scroll_one_page_until(self.page, dweb_category_ele.ele_filter_reset)
        # 再调一次，点击取消Delivery type = pantry
        self.category_filter_delivery_type(delivery_type_pantry)

    def add_to_global_product_cart_from_category(self):
        """
        该方法包含以下功能：
        1. 分类页加购global类型的商品
        """
        # 进入deals分类页
        self.go_to_special_category_from_hone(dweb_home_ele.ele_deals)
        # 滚动到指定位置
        scroll_one_page_until(self.page, dweb_category_ele.ele_filter_reset)
        # 点击Delivery type = global
        delivery_type_global = self.page.get_by_test_id(dweb_category_ele.global_delivery_test_id)
        # 滚动到指定位置
        scroll_one_page_until(self.page, dweb_category_ele.ele_filter_reset)
        self.category_filter_delivery_type(delivery_type_global)
        # 加购global售卖第一个商品
        product_ids = self.page.query_selector_all("//div[@data-testid='btn-atc-plus']")
        assert product_ids, f"本地售卖没有商品"
        for index, item in enumerate(product_ids):
            try:
                self.add_product_to_cart(item)
            except Exception as e:
                log.info("按钮加购失败" + str(e))
            if index == 1:
                break
        # 滚动到指定位置
        scroll_one_page_until(self.page, dweb_category_ele.ele_filter_reset)
        # 再调一次，点击取消Delivery type = global
        self.category_filter_delivery_type(delivery_type_global)

    def add_to_alcohol_product_cart_from_category(self):
        """
        该方法包含以下功能：
        1. 分类页加购alcohol类型的商品
        """
        # 进入deals分类页
        self.go_to_special_category_from_hone(dweb_home_ele.ele_deals)
        # 滚动到指定位置
        scroll_one_page_until(self.page, dweb_category_ele.ele_filter_reset)
        # 点击Delivery type = Local Delivery
        delivery_type_local = self.page.get_by_test_id(dweb_category_ele.local_delivery_test_id)
        self.category_filter_delivery_type(delivery_type_local)

        self.page.wait_for_timeout(2000)
        product_ids = self.page.query_selector_all("//div[@data-testid='btn-atc-plus']")
        assert product_ids, f"本地售卖没有商品"
        for index, item in enumerate(product_ids):
            try:
                self.add_product_to_cart(item)
            except Exception as e:
                log.info("按钮加购失败" + str(e))
            if index == 1:
                break

        # 滚动到指定位置
        scroll_one_page_until(self.page, dweb_category_ele.ele_filter_reset)
        # 再调一次，点击取消Delivery type = Local Delivery
        self.category_filter_delivery_type(delivery_type_local)
        # 点击Delivery type = pantry
        delivery_type_pantry = self.page.get_by_test_id(dweb_category_ele.pantry_delivery_test_id)
        # 滚动到指定位置
        scroll_one_page_until(self.page, dweb_category_ele.ele_filter_reset)
        # 点击pantry
        self.category_filter_delivery_type(delivery_type_pantry)
        # 加购pantry售卖第一个商品
        product_ids = self.page.query_selector_all("//div[@data-testid='btn-atc-plus']")
        assert product_ids, f"本地售卖没有商品"
        for index, item in enumerate(product_ids):
            try:
                self.add_product_to_cart(item)
            except Exception as e:
                log.info("按钮加购失败" + str(e))
            if index == 1:
                break
        # 滚动到指定位置
        scroll_one_page_until(self.page, dweb_category_ele.ele_filter_reset)
        # 再调一次，点击取消Delivery type = pantry
        self.category_filter_delivery_type(delivery_type_pantry)

        # 点击Delivery type = global
        delivery_type_global = self.page.get_by_test_id(dweb_category_ele.global_delivery_test_id)
        # 滚动到指定位置
        scroll_one_page_until(self.page, dweb_category_ele.ele_filter_reset)
        self.category_filter_delivery_type(delivery_type_global)
        # 加购global售卖第一个商品
        product_ids = self.page.query_selector_all("//div[@data-testid='btn-atc-plus']")
        assert product_ids, f"本地售卖没有商品"
        for index, item in enumerate(product_ids):
            try:
                self.add_product_to_cart(item)
            except Exception as e:
                log.info("按钮加购失败" + str(e))
            if index == 1:
                break
        # 滚动到指定位置
        scroll_one_page_until(self.page, dweb_category_ele.ele_filter_reset)
        # 再调一次，点击取消Delivery type = global
        self.category_filter_delivery_type(delivery_type_global)
        # 切换到酒分类
        self.special_category_filter_sub_category(dweb_category_ele.Alcohol)
        # 加购商品
        product_ids = self.page.query_selector_all("//div[@data-testid='btn-atc-plus']")
        assert product_ids, f"本地售卖没有商品"
        for index, item in enumerate(product_ids):
            try:
                self.add_product_to_cart(item)
            except Exception as e:
                log.info("按钮加购失败" + str(e))
            if index == 1:
                break

        # 8. 去购物车结算
        self.go_to_cart_from_category()


    def add_to_many_cart_from_category(self):
        """
        该方法包含以下功能：
        1. 分类页加购Deals类型的商品
        """
        # 进入deals分类页
        self.go_to_special_category_from_hone(dweb_home_ele.ele_deals)
        # 滚动到指定位置
        scroll_one_page_until(self.page, dweb_category_ele.ele_filter_reset)
        # 点击Delivery type = Local Delivery
        delivery_type_local = self.page.get_by_test_id(dweb_category_ele.local_delivery_test_id)
        self.category_filter_delivery_type(delivery_type_local)

        self.page.wait_for_timeout(2000)
        product_ids = self.page.query_selector_all("//div[@data-testid='btn-atc-plus']")
        assert product_ids, f"本地售卖没有商品"
        for index, item in enumerate(product_ids):
            try:
                self.add_product_to_cart(item)
            except Exception as e:
                log.info("按钮加购失败" + str(e))
            if index == 1:
                break

        # 滚动到指定位置
        scroll_one_page_until(self.page, dweb_category_ele.ele_filter_reset)
        # 再调一次，点击取消Delivery type = Local Delivery
        self.category_filter_delivery_type(delivery_type_local)
        # 点击Delivery type = pantry
        delivery_type_pantry = self.page.get_by_test_id(dweb_category_ele.pantry_delivery_test_id)
        # 滚动到指定位置
        scroll_one_page_until(self.page, dweb_category_ele.ele_filter_reset)
        # 点击pantry
        self.category_filter_delivery_type(delivery_type_pantry)
        # 加购pantry售卖第一个商品
        product_ids = self.page.query_selector_all("//div[@data-testid='btn-atc-plus']")
        assert product_ids, f"本地售卖没有商品"
        for index, item in enumerate(product_ids):
            try:
                self.add_product_to_cart(item)
            except Exception as e:
                log.info("按钮加购失败" + str(e))
            if index == 1:
                break
        # 滚动到指定位置
        scroll_one_page_until(self.page, dweb_category_ele.ele_filter_reset)
        # 再调一次，点击取消Delivery type = pantry
        self.category_filter_delivery_type(delivery_type_pantry)

        # 点击Delivery type = global
        delivery_type_global = self.page.get_by_test_id(dweb_category_ele.global_delivery_test_id)
        # 滚动到指定位置
        scroll_one_page_until(self.page, dweb_category_ele.ele_filter_reset)
        self.category_filter_delivery_type(delivery_type_global)
        # 加购global售卖第一个商品
        product_ids = self.page.query_selector_all("//div[@data-testid='btn-atc-plus']")
        assert product_ids, f"本地售卖没有商品"
        for index, item in enumerate(product_ids):
            try:
                self.add_product_to_cart(item)
            except Exception as e:
                log.info("按钮加购失败" + str(e))
            if index == 1:
                break
        # 滚动到指定位置
        scroll_one_page_until(self.page, dweb_category_ele.ele_filter_reset)
        # 再调一次，点击取消Delivery type = global
        self.category_filter_delivery_type(delivery_type_global)
        # 切换到酒分类
        self.special_category_filter_sub_category(dweb_category_ele.Alcohol)
        # 加购商品
        product_ids = self.page.query_selector_all("//div[@data-testid='btn-atc-plus']")
        assert product_ids, f"本地售卖没有商品"
        for index, item in enumerate(product_ids):
            try:
                self.add_product_to_cart(item)
            except Exception as e:
                log.info("按钮加购失败" + str(e))
            if index == 1:
                break

        # 8. 去购物车结算
        self.go_to_cart_from_category()
