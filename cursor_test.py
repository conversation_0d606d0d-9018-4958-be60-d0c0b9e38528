import pytest
from src.Dweb.EC.dweb_pages.dweb_page_address_management import AddressManagement
from src.config.weee.log_help import log


class TestAddressManagement:
    """测试从不同页面添加地址的功能"""

    @pytest.mark.address
    def test_add_address_from_home_page(self, page, header):
        """测试从首页添加新地址"""
        address_mgmt = AddressManagement(page, header)
        address_info = address_mgmt.add_address_from_home_page()
        assert address_info is not None, "从首页添加地址失败"

    @pytest.mark.address
    def test_add_address_from_checkout_page(self, page, header):
        """测试从结算页面添加新地址"""
        address_mgmt = AddressManagement(page, header)
        address_info = address_mgmt.add_address_from_checkout_page()
        assert address_info is not None, "从结算页面添加地址失败"

    @pytest.mark.address
    def test_add_address_from_order_details_page(self, page, header):
        """测试从订单详情页添加新地址"""
        address_mgmt = AddressManagement(page, header)
        address_info = address_mgmt.add_address_from_order_details_page()
        # 这个测试可能会被跳过，因为可能没有可用的订单或订单不支持修改地址
        if address_info is None:
            pytest.skip("没有可用的订单或订单不支持修改地址")
        assert address_info is not None, "从订单详情页添加地址失败"

    @pytest.mark.address
    def test_add_address_from_account_page(self, page, header):
        """测试从账户页面添加新地址"""
        address_mgmt = AddressManagement(page, header)
        address_info = address_mgmt.add_address_from_account_page()
        assert address_info is not None, "从账户页面添加地址失败"